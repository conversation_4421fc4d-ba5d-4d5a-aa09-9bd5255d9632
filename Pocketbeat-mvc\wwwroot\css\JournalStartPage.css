﻿#p_introduction {
    /* height: 90px; */
    font-size: 16px;
    /* min-width: 100%; */
    margin: 44px;
}

#img_1 {
    display: block;
    width: 100%;
}

Body {
    Text-Align: Justify;
    
    Margin: 0 Auto;
}

.Text {
    Font-Size: 24px;
}

.MoreText {
    Display: None;
}

.Read-More-Btn {
    Padding: 15px 60px;
    Background-Color: Rgb(149, 170, 197);
    Color: Rgb(53, 49, 49);
    Border: None;
    Outline: None;
    Font-Size: 20px;
    Cursor: Pointer;

}

.Text.Show-More .MoreText {
    Display: Inline;
}

.Text.Show-More .Dots {
    Display: None;
}

.div_description {
    max-height: 5.6em;
    overflow: hidden;
    white-space: pre-wrap;
}

    .div_description span:nth-of-type(2) {
        display: inline-block;
        background-color: #ccc;
    }


button.read-more-button {
    background: none;
    border: none;
    color: blue;
    cursor: pointer;
    text-decoration: underline;
}

.img1 {
    width: 100%;
}

.journal {

    margin-bottom:5%;
}

.DescriptionSection {
    display: block;
}

.DescriptionTruncated {
    width: 90%;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    color: black;
    font-size: 16px;
    margin: auto;
    margin-top: 1vw;
    margin-bottom: 1vw;

}


.DescriptionExtended {
    width: 90%;
    display: none;
    font-size: 16px;
    width: 90%;
    margin: auto;
    margin-top: 1vw;
    margin-bottom: 1vw;
}


.ReadMoreButton {
    width: 90%;
    display: block;
    margin: auto;
    background-color: white;
}





@media screen and (max-width: 400px) {

    .desktopImage {

        display:none;

    }


    .mobileImage{

        display:block;
    }


}


@media screen and (min-width: 401px) {

    .desktopImage {
        display: block;
    }


    .mobileImage {
        display: none;
    }
}


