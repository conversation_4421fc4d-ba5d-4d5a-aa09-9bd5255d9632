﻿using System.Drawing;
using System.Drawing.Imaging;





//string sourceFolderPath = @"C:\Users\<USER>\source\repos\Pocketbeat\Pocketbeat-mvc\Pocketbeat-mvc\wwwroot\Uploads";
//string destinationFolderPath = @"C:\Users\<USER>\source\repos\Pocketbeat\New folder";

//if (!Directory.Exists(destinationFolderPath))
//{
//    Directory.CreateDirectory(destinationFolderPath);
//}

//string[] imageFiles = Directory.GetFiles(sourceFolderPath, "*.jpg");

//foreach (string imagePath in imageFiles)
//{
//    string fileName = Path.GetFileName(imagePath);
//    string destinationPath = Path.Combine(destinationFolderPath, fileName);

//    using (Bitmap originalImage = new Bitmap(imagePath))
//    using (Bitmap newImage = new Bitmap(originalImage.Width, originalImage.Height))
//    {
//        for (int y = 0; y < originalImage.Height; y++)
//        {
//            for (int x = 0; x < originalImage.Width; x++)
//            {
//                Color pixelColor = originalImage.GetPixel(x, y);
//                if (pixelColor.R == 255 && pixelColor.G == 255 && pixelColor.B == 255) // White pixel
//                {
//                    newImage.SetPixel(x, y, Color.LightGreen);
//                }
//                else
//                {
//                    newImage.SetPixel(x, y, pixelColor);
//                }
//            }
//        }

//        newImage.Save(destinationPath);
//    }
//}





//meth("See You Again");
//meth("Sorry");
//meth("Despacito");
//meth("Baby");
//meth("Dark Horse");
//meth("Bad Guy");
//meth("Hotline Bling");
//meth("Love the Way");
//meth("Roar");
//meth("Work");
//meth("Hello");
//meth("Shape of You");
//meth("Gangnam Style");
//meth("My heart will go on");
//meth("Bumpy ride");
//meth("Thousand years");
//meth("Without You");
//meth("Numb");
//meth("Crawling");
//meth("Faint");
//meth("In the End");
//meth("My name is");
//meth("Rap God");

meth("one less lonely girl");
meth("Paper Cut");
meth("One step closer");
meth("lying from you");
meth("Umbrella");
meth("Superman");
meth("Lose yourself");
meth("Mockingbird");
meth("Rude boy");
meth("Diamonds");
meth("We Found love");
meth("Photograph");
meth("Thinking out loud");






int sdfsdf = 0;




static void meth(string videoName)
{
    CreateImageWithTextInCenter(1920, 1080, $"{videoName} ver.jpg", $"{videoName}");
    CreateImageWithTextInCenter(1080, 1920, $"{videoName} hor.jpg", $"{videoName}");
    CreateImageWithTextInCenter(1080, 1080, $"{videoName} sqr.jpg", $"{videoName}");
}






static void CreateImageWithTextInCenter(int width,int height, string saveFileName, string text)
{
    // Create a new Bitmap image with the given resolution
    Bitmap image = new Bitmap(width, height);

    // Create a Graphics object from the image
    using (Graphics graphics = Graphics.FromImage(image))
    {
        int rand = new Random().Next(0, 3);


        // Clear the image with a white background
        if (rand == 0)
        {
            graphics.Clear(Color.AliceBlue);
        }
        else if (rand == 1)
        {

            graphics.Clear(Color.LightGreen);
        }
        else if(rand ==2)
        {
            graphics.Clear(Color.LightSkyBlue);
        }

        // Set up the font and brush for drawing the text
        Font font = new Font("Arial", 96);
        SolidBrush brush = new SolidBrush(Color.Black);

        // Measure the size of the text
        SizeF textSize = graphics.MeasureString(text, font);

        // Calculate the position to center the text in the image
        float centerX = (image.Width - textSize.Width) / 2;
        float centerY = (image.Height - textSize.Height) / 2;

        // Draw the text in the center of the image
        graphics.DrawString(text, font, brush, centerX, centerY);
    }

    // Save the image to the disk with the provided file name
    image.Save(saveFileName, ImageFormat.Jpeg);
}