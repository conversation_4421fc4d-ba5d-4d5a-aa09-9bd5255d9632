﻿
/*container1*/
@media screen and (max-width: 584px) {

    .container1 {
       
    }
}

@media screen and (min-width: 585px) {

    .container1 {
       
    }
}



.artist_main {
    text-align: center;
    width: 100%;
    margin: auto;
    position: relative;
}

container1
.artist_main .back_button {
    width: 20px;
    height: 20px;
    position: absolute;
    top: 0;
    left: 0;
}




.artist_main .back_button:hover {
    background-color: lightgray;
}

@media screen and (max-width: 584px) {
    .track_title_and_artist {
        display: block;
        text-align: left;
        font-size: 3vw;
        margin-bottom: 0.5vw;
    }

}

@media screen and (min-width: 585px) {
    .track_title_and_artist {
        display: block;
        text-align: left;
        font-size: 1.2vw;
        margin-bottom: 0.5vw;
    }
}



@media screen and (max-width: 584px) {

    .artist_main .artist_name {
        display: inline-block;
        min-width: 100px;
        color: black;
        margin: 0;
        font-size: 4vw;
    }
}

@media screen and (min-width: 585px) {

    .artist_main .artist_name {
        display: inline-block;
        min-width: 100px;
        color: black;
        margin: 0;
        font-size: 2vw;
    }

}




.artist_main .artist_image {
    display: inline-block;
    width: 30%;
    min-width: 200px;
}



/*.button1 {
    width: 100%;
    display: block;
    margin: auto;
    background-color: white;
    text-size-adjust: auto;
}

    .button1:hover {
        background-color: lightgray;
    }*/

/*.related_styles {
    height: 32vw;
    text-align: left;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
}

    .related_styles .style_image {
        display: inline;
        height: 100%;
    }*/

.video-container {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 */
    height: 0;
    margin-bottom: 5vw;
}

    .video-container iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }

#sc_embedment_iframe {
    width: 100%;
    margin-bottom: 4vw;
}

@media screen and (max-width: 400px) {

    .desktopImage {
        display: none;
    }


    .mobileImage {
        display: block;
    }
}


@media screen and (min-width: 401px) {

    .desktopImage {
        display: block;
    }


    .mobileImage {
        display: none;
    }
}

.section_title {
    text-align: left;
    margin-top: 2vw;
    margin-bottom: 2vw;
    font-weight: 900;
}

.related_journal {
}

    .related_journal img {
        width: 100%;
    }



.section {
    margin-bottom: 5vw;
}

::-webkit-scrollbar {
    height: 4px; /* height of horizontal scrollbar ← You're missing this */
    width: 4px; /* width of vertical scrollbar */
    border: 1px solid #d5d5d5;
}

@media screen and (max-width: 584px) {

    .DescriptionExtended {
        width: 100%;
        display: none;
        font-size: 3vw;
        margin: auto;
        margin-top: 1vw;
        margin-bottom: 1vw;
        text-align: left;
    }

}


/*journal slide*/
.c {
    width: 100%;
    height: 500px;
    /* border-radius: 12px; */
    /* padding: 20px; */
    padding-bottom: 40px;
    /* box-shadow: 0 8px 48px 2px hsl(10deg 6% 15% / 40%); */
    display: flex;
    /* align-items: flex-end; */
    justify-content: center;
    position: relative;
    overflow: hidden;
    background: hsl(0 0% 90%);
    box-sizing: border-box;
}

.ci {
    position: absolute;
    top: 0;
    left: 0;
    width: inherit;
    height: inherit;
    transform-origin: left 50%;
    background: inherit;
    z-index: var(--z);
    transition: .3s ease-out;
}

    .ci img {
        -moz-user-select: none;
        user-select: none;
        height: 100%;
        width: 100%;
    }

.ch {
    position: absolute;
    top: 70%;
    left: 4%;
    transform: translateY(-50%);
    font-size: 2rem;
    color: hsla(var(--h) var(--s) var(--l) / .8);
    text-shadow: 0 2px 10px hsla(var(--h) var(--s) 10% / .3);
}


.slider_input {
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0;
    z-index: -10;
}

    .slider_input:last-child {
        margin-right: 0;
    }

    .slider_input:checked + label {
        /*background: linear-gradient(to right, hsla(var(--hue) 80% 70% / .7), hsla(calc(var(--hue) + 30) 80% 50% / .7));*/
        background-color: black;
    }

    .slider_input:not(:checked) + label + .ci {
        transform: translateX(-100%);
        opacity: 0;
    }

    .slider_input:checked + label + .ci ~ .ci {
        transform: translateX(100%);
    }

    .slider_input:not(:checked) + label + .ci {
        transition: 0;
    }

@media screen and (max-width: 584px) {

    .slider_label {
        width: 100%;
        height: 1.2vw;
        margin-right: 4px;
        /* border-radius: 20px; */
        background: hsla(0 0% 90% / .3);
        cursor: pointer;
        position: relative;
        z-index: 10;
    }
}

@media screen and (min-width: 585px) {

    .slider_label {
        width: 100%;
        height: 0.6vw;
        margin-right: 4px;
        /* border-radius: 20px; */
        background: hsla(0 0% 90% / .3);
        cursor: pointer;
        position: relative;
        z-index: 10;
    }
}


@media screen and (max-width: 584px) {

    .align_icons1 {
        margin-top: 2vw;
        margin-bottom: 9vw;
    }
}

@media screen and (min-width: 585px) {

    .align_icons1 {
        margin-top: 0vw;
        margin-bottom: 3.8vw;
    }
}



