﻿.container1 {
    text-align: center;
    max-width: 800px;
    padding: 5%;
    padding-top: 1%;
}

.main {
    text-align: center;
    width: 100%;
    margin: auto;
    position: relative;
}

    .main .back_button {
        width: 20px;
        height: 20px;
        position: absolute;
        top: 0;
        left: 0;
    }

        .main .back_button:hover {
            background-color: lightgray;
        }


.related_artists1 {
    height: 32vw;
    text-align: left;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
}

    .related_artists1 .artist_image {
        display: inline;
        height: 100%;
    }


.track_title_and_artist {
    display: block;
    text-align: left;
    /*margin-left: 2.5vw;*/
}

.streaming_services {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 4vw;
    gap: 4vw;
    justify-content: flex-start;
}

    .streaming_services .straming_service_icon {
        width: 35px;
        height: 35px;
    }


.main .track_name {
    display: inline-block;
    min-width: 100px;
    color: black;
    margin: 0;
}


.description {
    margin-top: 2vw;
    margin-bottom: 2vw;
    text-align: left;
    width: 100%;
}

.DescriptionTruncated {
    width: 100%;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    color: black;
    font-size: 16px;
    margin: auto;
    margin-top: 1vw;
    margin-bottom: 1vw;
    text-align: left;
}

.DescriptionExtended {
    width: 100%;
    display: none;
    font-size: 16px;
    margin: auto;
    margin-top: 1vw;
    margin-bottom: 1vw;
    text-align: left;
}


.button1 {
    width: 100%;
    display: block;
    margin: auto;
    background-color: white;
    text-size-adjust: auto;
}

    .button1:hover {
        background-color: lightgray;
    }

.related_styles {
    height: 32vw;
    text-align: left;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
}

    .related_styles .style_image {
        display: inline;
        height: 100%;
    }




.video-container {
    position: relative;
    padding-bottom: 56.25%;
    /* 16:9 */
    height: 0;
    margin-bottom: 5vw;
}

    .video-container iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }

.sc_embedment_container {
    width: 100%;
    margin-bottom: 4vw;
}

@media screen and (max-width: 400px) {

    .desktopImage {
        display: none;
        
    }


    .mobileImage {
        display: block;
        width: 100%;
    }
}


@media screen and (min-width: 401px) {

    .desktopImage {
        display: block;
        width: 100%;
    }


    .mobileImage {
        display: none;
        
    }
}

.section_title {
    text-align: left;
    margin-top: 2vw;
    margin-bottom: 2vw;
    font-weight: 900;
}

.related_journals {
}

    .related_journals img {
        width: 100%;
    }



.section {
    margin-bottom: 10vw;
}

::-webkit-scrollbar {
    height: 4px;
    /* height of horizontal scrollbar ← You're missing this */
    width: 4px;
    /* width of vertical scrollbar */
    border: 1px solid #d5d5d5;
}
