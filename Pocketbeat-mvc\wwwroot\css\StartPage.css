﻿/*email prompt*/
@media screen and (max-width: 584px) {

   /* #email-prompt-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 9999;
        display: none;
    }*/

    #email-prompt {
        position: fixed;
        top: 55vw;
        left: 1vw;
        right: 2vw;
        background-color: black;
        z-index: 10000;
        border-radius: 5px;
        padding: 10px;
        display: none;
        height: 300px;
    }


    #prompt .prompt-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #ccc;
        padding: 5px;
    }

        #prompt .prompt-header .prompt-title {
            font-size: 1.2rem;
        }

        #prompt .prompt-header .prompt-close {
            cursor: pointer;
            font-size: 1.5rem;
        }

    #prompt .prompt-body {
        padding: 10px;
    }

    #prompt input,
    #prompt button {
        margin: 5px;
        padding: 5px;
    }

    #prompt button {
        background-color: #4CAF50;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
    }

        #prompt button:hover {
            background-color: #3e8e41;
        }


    #div_new {
        margin: auto;
        width: 75%;
        text-align: center;
        display: block;
        height: 15vw;
    }

    .country {
        height: 7vw;
        margin-left: 0;
        display: block;
        color: white;
        background-color: black;
        margin-top: 12vw;
        font-size: 3vw;
    }


    #input_country {
        display: none;
        width: 100%;
        height: 219px;
        margin: auto;
        border-radius: 4px;
        border-style: none;
        background-color: #222;
        margin-bottom: 5px;
        position: relative;
    }

    #under_country {
        font-size: 3vw;
        margin: 3px;
        color: white;
        text-align: left;
        margin-left: 7px;
        margin: auto;
        margin-top: 10px;
        margin-bottom: 20px;
    }

    #input_div2 {
        text-align: center;
        margin: auto;
        height: 4vw;
        width: 100%;
        height: 50px;
    }

    #cross_button {
        position: absolute;
        background-color: #222;
        color: white;
        top: 0;
        right: 0;
    }

        #cross_button:active {
            background-color: white;
        }


    .prompt_email_txt {
        width: 70%;
        float: left;
        display: inline;
        margin: auto;
        height: 68%;
        font-size: 3vw;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        border: solid 1px black;
        text-align: left;
        /* margin-right: 2px; */
        padding-left: 10px;
        border-radius: 11px;
    }

    .prompt_email_button {
        margin: auto;
        background-color: #222;
        border-radius: 4px;
        border-style: solid;
        box-sizing: border-box;
        /* border-color: white; */
        color: #fff;
        cursor: pointer;
        display: inline-block;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        font-size: 3vw;
        font-weight: 700;
        line-height: 1.5;
        margin: 0;
        max-width: none;
        outline: none;
        overflow: hidden;
        position: relative;
        text-align: center;
        text-transform: none;
        user-select: none;
        -webkit-user-select: none;
        touch-action: manipulation;
        height: 80%;
        margin-top: -0.1vw;
        flex-grow: 1;
        /* width: 25%;*/
    }

}

@media screen and (min-width: 585px) {
    #email-prompt-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 9999;
        display: none;
    }

    #email-prompt {
        position: fixed;
        top: 20vw;
        left: 0vw;
        right: 0vw;
        background-color: black;
        z-index: 10000;
        border-radius: 5px;
        padding: 10px;
        display: none;
        height: 22vw;
    }

    
    #prompt .prompt-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #ccc;
        padding: 5px;
    }

        #prompt .prompt-header .prompt-title {
            font-size: 1.2rem;
        }

        #prompt .prompt-header .prompt-close {
            cursor: pointer;
            font-size: 1.5rem;
        }

    #prompt .prompt-body {
        padding: 10px;
    }

    #prompt input,
    #prompt button {
        margin: 5px;
        padding: 5px;
    }

    #prompt button {
        background-color: #4CAF50;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
    }

        #prompt button:hover {
            background-color: #3e8e41;
        }


    #div_new {
        margin: auto;
        margin-top: 3vw;
        width: 55vw;
        text-align: center;
        display: block;
        height: 20vw;
    }

    .country {
        height: 3vw;
        font-size: 1.vw;
        margin-left: 0;
        display: block;
        color: white;
        background-color: black;
    }


    #input_country {
        display: none;
        width: 100%;
        height: 219px;
        margin: auto;
        border-radius: 4px;
        border-style: none;
        background-color: #222;
        margin-bottom: 5px;
        position: relative;
    }

    #under_country {
        font-size: 1.5vw;
        margin: 3px;
        color: white;
        text-align: left;
        margin-left: 7px;
        margin: auto;
        margin-top: 10px;
        margin-bottom: 1vw;
    }

    #input_div2 {
        text-align: center;
        margin: auto;
        height: 4vw;
        width: 100%;
    }


    #cross_button {
        position: absolute;
        background-color: #222;
        color: white;
        top: 0;
        right: 0;
    }

        #cross_button:active {
            background-color: white;
        }

    .prompt_email_txt {
        width: 75%;
        float: left;
        display: inline;
        margin: auto;
        height: 68%;
        font-size: 1.2vw;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        border: solid 1px black;
        text-align: left;
        margin-right: 2px;
        padding-left: 10px;
        border-radius: 11px;
    }

    .prompt_email_button {
        margin: auto;
        background-color: #222;
        border-radius: 4px;
        border-style: solid;
        box-sizing: border-box;
        /* border-color: white; */
        color: #fff;
        cursor: pointer;
        display: inline-block;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        font-size: 1.2vw;
        font-weight: 700;
        line-height: 1.5;
        margin: 0;
        max-width: none;
        outline: none;
        overflow: hidden;
        position: relative;
        text-align: center;
        text-transform: none;
        user-select: none;
        -webkit-user-select: none;
        touch-action: manipulation;
        height: 80%;
        margin-top: -0.1vw;
        flex-grow: 1;
        width: 12vw;
    }
}



.text_ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}




/*container1*/
@media screen and (max-width: 584px) {

    .container1 {
    }
}

@media screen and (min-width: 585px) {

    .container1 {
       
       
    }
}



/*promo track title*/
@media screen and (max-width: 584px) {


    #promo_track_title {
        text-align: center;
        margin-top: 5vw;
        margin-bottom: 1.5vw;
        font-size: 5vw;
    }
}

@media screen and (min-width: 585px) {


    #promo_track_title {
        text-align: center;
        margin-top: 1vw;
        margin-bottom: 0.5vw;
        font-size: 2.3vw;
    }
}


/*top promo image*/
@media screen and (max-width: 584px) {


    #promo_track_image {
        margin-bottom:2vw;

    }
}
@media screen and (min-width: 585px) {


    #promo_track_image {
        margin-bottom: 2vw;
    }
}



/*promo track artists*/
@media screen and (max-width: 584px) {


    #promo_track_artists {
        text-align: center;
        margin-bottom: 5vw;
        font-size: 5vw;
        font-style: italic;
    }
}

@media screen and (min-width: 585px) {


    #promo_track_artists {
        text-align: center;
        margin-bottom: 2vw;
        font-size: 1.5vw;
        font-style:italic;
    }
}




/*ovarlay text*/
@media screen and (max-width: 584px) {


    .overlay_text {
        color: white;
        font-size: 10vw;
        font-weight: 900;
        text-shadow: 3px 3px black
    }
}

@media screen and (min-width: 585px) {


    .overlay_text {
        color: white;
        font-size: 5vw;
        font-weight: 900;
        text-shadow: 3px 3px black
    }
}





/*div_enter_email_address*/
@media screen and (max-width: 584px) {

    #div_enter_email_address {
        margin-left: 3vw;
        margin-top: 8vw;
        font-size: 3vw;
        text-align: left;
        margin-bottom: 12px;
    }
}

@media screen and (min-width: 585px) {

    #div_enter_email_address {
        margin-top: 4vw;
        font-size: 1.2vw;
        text-align: left;
        margin-bottom: 1vw;
        margin-left: 1vw;
    }
}












/*email textbox and button*/
@media screen and (max-width: 584px) {

    .email_txt_and_button {
        height: 9.5vw;
        margin-top: 1px;
        margin-bottom: 2vw;
        width: 100%;
        margin-left: auto;
        margin-right: auto;
        display: flex;
    }

    .email_txt {
        width: 70%;
        float: left;
        display: inline;
        margin: auto;
        height: 85%;
        font-size: 3.2vw;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        border: solid 1px black;
        text-align: left;
        margin-right: 2px;
        padding-left: 10px;
        border-radius: 11px;
        font-style: italic;
    }

    .email_button {
        margin: auto;
        background-color: #222;
        border-radius: 4px;
        border-style: solid;
        box-sizing: border-box;
        border-color: white;
        color: #fff;
        cursor: pointer;
        display: inline-block;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        font-size: 3.2vw;
        font-weight: 700;
        line-height: 1.5;
        margin: 0;
        max-width: none;
        outline: none;
        overflow: hidden;
        position: relative;
        text-align: center;
        text-transform: none;
        user-select: none;
        -webkit-user-select: none;
        touch-action: manipulation;
        height: 98%;
        margin-top: -0.1vw;
        flex-grow: 1;
    }
}

@media screen and (min-width: 585px) { /*same as the earlier one , only font sizes change
                                            to 2.5vw from 3.5vw
                                        */

    .email_txt_and_button {
        height: 3.5vw;
        margin-top: 1px;
        margin-bottom: 2vw;
        width: 100%;
        margin-left: auto;
        margin-right: auto;
        display:flex;
    }

    .email_txt {
        width: 83%;
        float: left;
        display: inline;
        margin: auto;
        height: 85%;
        font-size: 1.2vw;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        border: solid 1px black;
        text-align: left;
        margin-right: 2px;
        padding-left: 10px;
        border-radius: 11px;
        font-style:italic;
    }

    .email_button {
        margin: auto;
        background-color: #222;
        border-radius: 4px;
        border-style: solid;
        box-sizing: border-box;
        /*border-color: white;*/
        color: #fff;
        cursor: pointer;
        display: inline-block;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        font-size: 1.2vw;
        font-weight: 700;
        line-height: 1.5;
        margin: 0;
        max-width: none;
        outline: none;
        overflow: hidden;
        position: relative;
        text-align: center;
        text-transform: none;
        user-select: none;
        -webkit-user-select: none;
        touch-action: manipulation;
        height: 98%;
        margin-top: -0.1vw;
        flex-grow:1;
    }
    
}









/*want to catch the stockhom soundrom?*/
@media screen and (max-width: 584px) {

    #catch_stock_soundrom {
        font-size: 3.7vw;
        text-align: left;
        margin-bottom: 0.4vw;
        font-weight: bold;
        margin-left: 1vw;
    }
}

@media screen and (min-width: 585px) {

    #catch_stock_soundrom {
        font-size: 1.2vw;
        text-align: left;
        margin-bottom: 0.4vw;
        font-weight: bold;
        margin-left:1vw;
    }
}


/*here are some ways to catch it*/
@media screen and (max-width: 584px) {

    #some_ways_to_catch {
        font-size: 3.7vw;
        margin-bottom: 1.5vw;
        font-weight: bold;
        margin-left: 1vw;
    }
}

@media screen and (min-width: 585px) {

    #some_ways_to_catch {
        font-size: 1.1vw;
        margin-bottom: 1.5vw;
        font-weight: bold;
        margin-left: 1vw;
    }
}


/*all_tracks_slides*/
@media screen and (max-width: 584px) {

    .all_tracks_slides {
        height: 55vw;
        text-align: left;
        overflow-x: auto;
        overflow-y: hidden;
        white-space: nowrap;
        font-size:0;
    }
}

@media screen and (min-width: 585px) {

    .all_tracks_slides {
        height: 16vw;
        text-align: left;
        overflow-x: auto;
        overflow-y: hidden;
        white-space: nowrap;
        font-size:0;
    }
}

/*track image and info*/
@media screen and (max-width: 584px) {

    .track_img_and_info {
        display: inline-block;
        height: 100%;
        width: 44vw;
    }
}

@media screen and (min-width: 585px) {

    .track_img_and_info {
        display: inline-block;
        height: 100%;
        width:12.8vw;
        
    }
}


/*track image*/
@media screen and (max-width: 584px) {

    .track_img {
        height: 80%;
    }
}

@media screen and (min-width: 585px) {

    .track_img {
        height: 80%;
    }
}

/*track info*/
@media screen and (max-width: 584px) {

    .track_info {
        height: 20%;
    }
}

@media screen and (min-width: 585px) {

    .track_info {
        height: 20%;
    }
}


/*track title*/
@media screen and (max-width: 584px) {

    .track_title {
        text-align: center;
        font-weight: bold;
        font-size: 3.2vw;
        font-style:italic;
    }
}

@media screen and (min-width: 585px) {

    .track_title {
        text-align: center;
        font-weight: bold;
        font-size: 1vw;
        font-style: italic;
    }
}

/*track artist name*/
@media screen and (max-width: 584px) {

    .track_artist_name {
        text-align: center;
        font-size: 3.2vw;
        font-style: italic;
    }
}

@media screen and (min-width: 585px) {

    .track_artist_name {
        text-align: center;
        font-size: 1vw;
        font-style: italic;
    }
}



.c {
    width: 100%;
    height: 500px;
    /* border-radius: 12px; */
    /* padding: 20px; */
    padding-bottom: 40px;
    /* box-shadow: 0 8px 48px 2px hsl(10deg 6% 15% / 40%); */
    display: flex;
    /* align-items: flex-end; */
    justify-content: center;
    position: relative;
    overflow: hidden;
    background: hsl(0 0% 90%);
    box-sizing: border-box;
}




.ci {
    position: absolute;
    top: 0;
    left: 0;
    width: inherit;
    height: inherit;
    transform-origin: left 50%;
    background: inherit;
    z-index: var(--z);
    transition: .3s ease-out;
}

    .ci img {
        -moz-user-select: none;
        user-select: none;
        height: 100%;
        width: 100%;
    }

.ch {
    position: absolute;
    top: 70%;
    left: 4%;
    transform: translateY(-50%);
    font-size: 2rem;
    color: hsla(var(--h) var(--s) var(--l) / .8);
    text-shadow: 0 2px 10px hsla(var(--h) var(--s) 10% / .3);
}


.slider_input {
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0;
    z-index: -10;
}

    .slider_input:last-child {
        margin-right: 0;
    }

    .slider_input:checked + label {
        /*background: linear-gradient(to right, hsla(var(--hue) 80% 70% / .7), hsla(calc(var(--hue) + 30) 80% 50% / .7));*/
        background-color:black;
    }

    .slider_input:not(:checked) + label + .ci {
        transform: translateX(-100%);
        opacity: 0;
    }

    .slider_input:checked + label + .ci ~ .ci {
        transform: translateX(100%);
    }

    .slider_input:not(:checked) + label + .ci {
        transition: 0;
    }



@media screen and (max-width: 584px) {

    .slider_label {
        width: 100%;
        height: 1.2vw;
        margin-right: 4px;
        /* border-radius: 20px; */
        background: hsla(0 0% 90% / .3);
        cursor: pointer;
        position: relative;
        z-index: 10;
    }
}

@media screen and (min-width: 585px) {

    .slider_label {
        width: 100%;
        height: 0.6vw;
        margin-right: 4px;
        /* border-radius: 20px; */
        background: hsla(0 0% 90% / .3);
        cursor: pointer;
        position: relative;
        z-index: 10;
    }
}



    #input_div1 {
        margin: auto;
        margin-bottom: 20px;
        height: 40px;
    }

    #txt {
        display: inline;
        margin: auto;
        height: 100%;
        font-size: 3.5vw;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        border: solid 1px black;
        text-align: left;
        margin-right: 3px;
        padding-left: 1vw;
    }

    #btn {
        margin: auto;
        background-color: #222;
        border-radius: 4px;
        border-style: none;
        box-sizing: border-box;
        color: #fff;
        cursor: pointer;
        display: inline-block;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        font-size: 3.5vw;
        font-weight: 700;
        line-height: 1.5;
        margin: 0;
        max-width: none;
        /* min-height: 44px; */
        /* min-width: 10px; */
        outline: none;
        overflow: hidden;
        /* padding: 9px 20px 8px; */
        position: relative;
        text-align: center;
        text-transform: none;
        user-select: none;
        -webkit-user-select: none;
        touch-action: manipulation;
        /* font-size: 16px; */
        /* min-width: 100%; */
        /* margin: 44px; */
        height: 100%;
    }


    @media screen and (max-width: 480px) {

        .desktopImage {
            display: none;
        }


        .mobileImage {
            display: block;
        }

        .desktop_img_overlay {
            display: none;
        }

        .mobile_img_overlay {
            display: block;
        }
    }


    @media screen and (min-width: 481px) {

        .desktopImage {
            display: block;
        }


        .mobileImage {
            display: none;
        }

        .desktop_img_overlay {
            display: block;
        }

        .mobile_img_overlay {
            display: none;
        }
    }

    @media screen and (max-width: 650px) {

        .profile_container {
            text-align: center;
            margin: auto;
        }
    }


    @media screen and (min-width: 651px) {

        .profile_container {
            display: grid;
            grid-template-columns: 50% 50%;
            width: 80%;
            margin: auto;
        }
    }

    #p_introduction {
        /* height: 90px; */
        font-size: 16px;
        /* min-width: 100%; */
        margin: 44px;
    }


    li {
        padding: 10px;
        margin: 10px;
        cursor: pointer;
    }

    #side_nav_bar {
        position: fixed;
        top: 0;
        left: 0;
        width: 200px;
        height: 100%;
        background-color: #2b2e2f;
        color: #FFF;
        padding: 1em;
    }


    body {
    }


    form {
        position: relative;
    }

    #add_entity {
        position: relative;
        padding: 50px;
    }


        #add_entity .close-button {
            position: absolute;
            top: 0;
            right: 50px;
            cursor: pointer;
            background-color: black;
            border: solid;
            color: white;
        }

    #lbl_entity {
        color: white;
    }


    .btn1 {
        color: white;
        background-color: black;
        border-radius: 3px;
        border-color: white;
        padding-top: 11px;
        padding-bottom: 10px;
        padding-left: 20px;
        padding-right: 20px;
    }





    .profile_box {
        height: 250px;
        width: 250px;
        display: inline-block;
        text-align: justify;
        margin: 14px;
    }

        .profile_box .cover_art_with_vinyls {
            cursor: pointer;
            transition: all 0.5s ease;
        }

            .profile_box .cover_art_with_vinyls:hover {
                transform: scale(1.1);
            }

            .profile_box .cover_art_with_vinyls img {
                height: 200px;
            }



        .profile_box .entity_name {
            /*text-align: right;*/
            color: black;
            margin-bottom: 6px;
            margin-top: 3px;
            font-size: small;
            font-weight: 600;
            display: inline-block;
            width: 150px;
            white-space: nowrap;
            overflow: hidden !important;
            text-overflow: ellipsis;
            text-align: center;
        }

        .profile_box .related_entity {
            width: 150px;
            text-align: center;
            color: black;
            margin-bottom: 3px;
            display: block;
            font-size: x-small;
        }

        .profile_box .state {
            border: solid;
            color: white;
            display: block;
            width: 150px;
        }

    .infobox {
        margin-top: 15px;
        /*margin-left: 50px;*/
        /* height: 60px;*/
        min-height: 60px;
        background-color: black;
        color: white;
        border: solid white;
        border-width: 1px;
    }

        .infobox label {
            width: 100%;
            height: 10px;
            font-size: x-small;
            font-size: x-small;
            display: block;
        }

        .infobox input {
            width: 90%;
            font-size: large;
            background-color: black;
            color: white;
            border: none;
        }

        .infobox .input_list {
            border: solid;
            border-color: #423a3a;
            margin-bottom: 2px;
        }

        .infobox textarea {
            font-size: large;
            background-color: black;
            color: white;
            border: none;
            width: 700px;
            height: 140px;
        }


    .button_container {
        /*margin-left: 50px;*/
        margin-top: 20px;
    }

    #pic_vertical {
        width: 180px;
        height: 180px;
        position: relative;
        border: solid;
        border-color: white;
        color: white;
        /*margin-left: 50px;*/
        vertical-align: central;
        display: inline-block;
        background-size: 100% 100%;
        margin-top: 15px;
    }

        #pic_vertical:hover {
            background-color: #5d6952;
        }

    #pic_horizontal {
        width: 200px;
        height: 130px;
        position: relative;
        border: solid;
        border-color: white;
        color: white;
        display: inline-block;
        background-size: 100% 100%;
    }

        #pic_horizontal:hover {
            background-color: #5d6952;
        }




    .plus {
        font-size: 73px;
        text-align: center;
        margin: auto;
        color: white;
        font-weight: 900;
    }


    .add_another {
        display: inline;
        text-align: right;
        color: white;
        position: absolute;
        right: 0px;
        font-size: x-small;
    }

        .add_another:hover {
            color: blue;
        }

    .select_related {
        background-color: black;
        color: white;
        margin: 3px;
        position: relative;
    }

    /*.select_related .close-button {
                position: absolute;
                top: 0;
                right: 0;
                display: none;
            }

            .select_related:hover .close-button {
                display: block;
            }

        .close-button {*/
    /* style the close button, such as color, size, etc. */
    /*color:red;
        }*/


    /*.parent {
    z-index: 1;
    background-image: url('http://img.freepik.com/free-vector/white-canvas-background_1053-239.jpg?size=338&ext=jpg');
}

.child {
    position: absolute;
    z-index: 1;
}*/



    .remove {
        position: absolute;
        top: 0px;
        right: 0px;
        display: block;
        box-sizing: border-box;
        width: 20px;
        height: 20px;
        border-width: 3px;
        border-style: solid;
        border-color: red;
        border-radius: 100%;
        background: -webkit-linear-gradient(-45deg, transparent 0%, transparent 46%, white 46%, white 56%,transparent 56%, transparent 100%), -webkit-linear-gradient(45deg, transparent 0%, transparent 46%, white 46%, white 56%,transparent 56%, transparent 100%);
        background-color: red;
        box-shadow: 0px 0px 5px 2px rgba(0,0,0,0.5);
        transition: all 0.3s ease;
    }

    .img1 {
        width: 100%;
    }














    .DescriptionSection {
        display: block;
    }



    .ReadMoreButton {
        width: 90%;
        display: block;
        margin: auto;
        background-color: white;
        border-radius: 6px;
    }







    #input_country {
        width: 100%;
        height: 219px;
        margin: auto;
        border-radius: 4px;
        border-style: none;
        background-color: #222;
        margin-bottom: 5px;
        position: absolute;
        display: none;
    }



    /*.container {
    display: table-cell;
    position: relative;*/
    /* top: 5vw; */
    /*width: 60vw;*/
    /*height: 500vh;*/
    /*position: relative;
    top: 5vw;
    left: 19vw;
    z-index: 0;
}


@media screen and (min-width: 1333px) {

    .container {
        display: table-cell;
        position: relative;*/
    /* top: 5vw; */


    /*position: relative;
        top: 5vw;
        left: calc(100vw/2 - 400px);
        z-index: 0;
        width: 800px;
    }
}


@media screen and (max-width: 584px) {

    

    .container {
        display: table-cell;
        position: relative;*/
    /* top: 5vw; */
    /*width: 100vw;
        position: relative;
        top: 5vw;
        left: 0;
        z-index: 0;
    }
}*/


    /*parties*/

    @media screen and (max-width: 480px) {

        .party-box {
            box-shadow: 2px 3px;
            position: absolute;
            top: 10%;
            left: 50%;
            border: solid white;
            z-index: 0;
            height: 33%;
            width: 33%;
            transform: translate(-50%,0);
            border-radius: 5%;
        }

        .party-box-city {
            text-shadow: 1px 1px black;
            color: white;
            position: absolute;
            top: 7%;
            left: 50%;
            /* transition: t; */
            transform: translate(-50%,0);
            font-size: 3vw;
            font-style: italic;
        }

        .party-box-month {
            text-shadow: 2px 2px black;
            color: white;
            position: absolute;
            top: 23%;
            left: 50%;
            /* transition: t; */
            transform: translate(-50%,0);
            font-size: 5vw;
            font-weight: 600;
        }

        .party-box-date {
            text-shadow: 2px 2px black;
            color: white;
            position: absolute;
            top: 42%;
            left: 50%;
            /* transition: t; */
            transform: translate(-50%,0);
            font-size: 8vw;
        }

        .party-box-start-and-endtime {
            text-shadow: 1px 1px black;
            color: white;
            position: absolute;
            top: 80%;
            left: 50%;
            /* transition: t; */
            transform: translate(-50%,0);
            font-size: 3vw;
            width: 30vw;
            text-align: center;
        }

        .party-top-title {
            text-shadow: 2px 2px black;
            color: white;
            position: absolute;
            top: 48%;
            left: 12%;
            font-size: 4vw;
            width: 50%;
            display: block;
            font-weight: 600;
        }

        .party-main-title-and-subtitle {
            position: absolute;
            left: 12%;
            top: 55%;
            width: 85%;
        }

        .party-main-title {
            text-shadow: 3px 3px black;
            color: white;
            font-size: 8vw;
            font-weight: 600;
            line-height :1;
        }

        .party-sub-title {
            text-shadow: 2px 2px black;
            color: white;
            font-size: 5vw;
            font-style: italic;
            line-height: 1.2;
            margin-top: 2vw;
        }
    }


    @media screen and (max-width: 584px) and (min-width: 481px) {

        .party-box {
            box-shadow: 2px 3px;
            position: absolute;
            top: 50%;
            left: 10%;
            border: solid white;
            z-index: 0;
            height: 33.33%;
            width: 18.75%;
            transform: translate(0,-50%);
            border-radius: 5%;
        }

        .party-box-city {
            text-shadow: 1px 1px black;
            color: white;
            position: absolute;
            top: 7%;
            left: 50%; /* transition: t; */
            transform: translate(-50%,0);
            font-size: 2vw;
            font-style: italic;
        }

        .party-box-month {
            text-shadow: 2px 2px black;
            color: white;
            position: absolute;
            top: 26%;
            left: 50%;
            /* transition: t; */
            transform: translate(-50%,0);
            font-size: 3vw;
            font-weight: 600;
        }

        .party-box-date {
            text-shadow: 2px 2px black;
            color: white;
            position: absolute;
            top: 50%;
            left: 50%; /* transition: t; */
            transform: translate(-50%,0);
            font-size: 3vw;
        }

        .party-box-start-and-endtime {
            text-shadow: 1px 1px black;
            color: white;
            position: absolute;
            top: 80%;
            left: 50%; /* transition: t; */
            transform: translate(-50%,0);
            font-size: 2vw;
            width: 15vw;
            text-align: center;
        }

        .party-top-title {
            text-shadow: 2px 2px black;
            color: white;
            position: absolute;
            top: 23%;
            left: 10%;
            font-size: 3vw;
            width: 50%;
            display: block;
            font-weight: 600;
        }

        .party-main-title-and-subtitle {
            position: absolute;
            left: 32%;
            top: 33%;
            width: 65%;
        }

        .party-main-title {
            text-shadow: 3px 3px black;
            color: white;
            font-size: 5.5vw;
            font-weight: 600;
            line-height:1;
        }

        .party-sub-title {
            text-shadow: 2px 2px black;
            color: white;
            font-size: 3vw;
            font-style: italic;
        }
    }

    @media screen and (min-width: 585px) {
        .party-box {
            box-shadow: 2px 3px;
            position: absolute;
            top: 50%;
            left: 10%;
            border: solid white;
            z-index: 0;
            height: 33.33%;
            width: 18.75%;
            transform: translate(0,-50%);
            border-radius: 5%;
        }

        .party-box-city {
            text-shadow: 2px 2px black;
            color: white;
            position: absolute;
            top: 7%;
            left: 50%; /* transition: t; */
            transform: translate(-50%,0);
            font-size: 1.2vw;
            font-style: italic;
        }

        .party-box-month {
            text-shadow: 2px 2px black;
            color: white;
            position: absolute;
            top: 22%;
            left: 50%; /* transition: t; */
            transform: translate(-50%,0);
            font-size: 2vw;
            font-weight: 600;
        }

        .party-box-date {
            text-shadow: 2px 2px black;
            color: white;
            position: absolute;
            top: 43%;
            left: 50%; /* transition: t; */
            transform: translate(-50%,0);
            font-size: 3vw;
        }

        .party-box-start-and-endtime {
            text-shadow: 2px 2px black;
            color: white;
            position: absolute;
            top: 80%;
            left: 50%; /* transition: t; */
            transform: translate(-50%,0);
            font-size: 1.2vw;
            width: 9vw;
            text-align: center;
        }

        .party-top-title {
            text-shadow: 2px 2px black;
            color: white;
            position: absolute;
            top: 23%;
            left: 10%;
            font-size: 2vw;
            width: 50%;
            display: block;
            font-weight: 600;
        }

        .party-main-title-and-subtitle {
            position: absolute;
            left: 32%;
            top: 33%;
            width: 65%;
        }

        .party-main-title {
            text-shadow: 3px 3px black;
            color: white;
            font-size: 3.2vw;
            font-weight: 600;
            line-height:1;
        }

        .party-sub-title {
            text-shadow: 2px 2px black;
            color: white;
            font-size: 2vw;
            font-style: italic;
        }
    }

