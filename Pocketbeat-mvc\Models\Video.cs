﻿namespace Pocketbeat_mvc.Models
{
    public class Video
    {
        public int Id { get; set; }
        public string YoutubeLink { get; set; }
        public string VideoTitle { get; set; }
        public List<Artist> RelatedArtists { get; set; } = new List<Artist>();

        public string Genres { get; set; }

        public bool TopPosition { get; set; }
        public string VerticalImage { get; set; }
        public string HorizontalImage { get; set; }
        public string SquareImage { get; set; }
        public string VerticalImageSmall { get; set; }
        public string HorizontalImageSmall { get; set; }
        public string SquareImageSmall { get; set; }
        public DateTime CreatedDate { get; set; }
    }
}
