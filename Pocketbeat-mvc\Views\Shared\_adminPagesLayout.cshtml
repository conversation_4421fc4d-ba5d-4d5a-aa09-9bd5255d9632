﻿@using Microsoft.AspNetCore.Http;

@{
    var userId = 1;
    var username = "";

    //using (Data data = new Data())
    //{
    //    username = data.Admins.Where(a => a.Id == userId).First().Email;
    //}

}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - stockhom_soundrom_mvc</title>
    <link href='https://fonts.googleapis.com/css?family=Oswald' rel='stylesheet'>
    <link href='https://fonts.googleapis.com/css?family=Lato:400,700' rel='stylesheet' type='text/css'>
    <link href='https://fonts.googleapis.com/css?family=Permanent Marker' rel='stylesheet'>
    <link rel="stylesheet" href="~/css/site.css" />
    <script src="~/lib/jquery/dist/jquery.min.js"></script>

    @RenderSection("Styles", required: false)


    <style>

        a {
            color: inherit;
            text-decoration: none;
        }


        li {
            padding: 10px;
            margin: 10px;
            cursor: pointer;
        }

        #side_nav_bar {
            width: 200px;
            height: 100%;
            background-color: #2b2e2f;
            color: #FFF;
        }

        td {
            border: 1px solid black;
            padding-bottom: 10%;
        }


        #container_main1 {
            width: 100%;
            display: table;
        }


        #container_main2 {
            display: table-row;
        }


        @@media screen and (min-width: 1600px) {

            #left {
                display: table-cell;
            }


            #container1 {
                display: table-cell;
                width: 800px;
            }


            #right {
                display: table-cell;
            }
        }


        @@media screen and (min-width: 800px) and (max-width: 1599px) {

            #left {
                display: table-cell;
            }


            #container1 {
                display: table-cell;
                width: calc(40vw + 100px);
            }


            #right {
                display: table-cell;
            }
        }

        @@media screen and (max-width: 799px) {


            #container_main1 {
                width: 100%;
                display: block;
            }


            #container_main2 {
                display: block;
                width: 100%;
            }

            #left {
                display: none;
            }


            #container1 {
                display: block;
                width: 100%;
            }


            #right {
                display: none;
            }
        }

        @@media (max-width: 2000px) {
            .menu-button-container {
                display: flex;
            }

            .menu {
                position: absolute;
                top: 0;
                margin-top: 50px;
                left: 0;
                flex-direction: column;
                width: 100%;
                justify-content: center;
                align-items: center;
            }

            #menu-toggle ~ .menu li {
                height: 0;
                margin: 0;
                padding: 0;
                border: 0;
                transition: height 400ms cubic-bezier(0.23, 1, 0.32, 1);
            }

            #menu-toggle:checked ~ .menu li {
                border: 1px solid #333;
                height: 2.5em;
                padding: 0.5em;
                transition: height 400ms cubic-bezier(0.23, 1, 0.32, 1);
            }

            .menu > li {
                display: flex;
                justify-content: center;
                margin: 0;
                padding: 0.5em 0;
                width: 100%;
                color: white;
                background-color: #222;
            }

                .menu > li:not(:last-child) {
                    border-bottom: 1px solid #444;
                }
        }






        h2 {
            vertical-align: center;
            text-align: center;
        }

        html,
        body {
            margin: 0;
            height: 100%;
        }

        * {
            box-sizing: border-box;
        }

        .top-nav {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            background-color: lightblue;
            color: #FFF;
            height: 50px;
            padding: 1em;
        }

            .top-nav img {
                height: 30px;
                position: absolute;
                top: 20px;
                left: 50%;
                transform: translate(-50%, -50%);
            }

        .menu {
            display: flex;
            flex-direction: row;
            list-style-type: none;
            margin: 0;
            padding: 0;
        }

            .menu > li {
                margin: 0 1rem;
                overflow: hidden;
            }

        .menu-button-container {
            display: none;
            height: 100%;
            width: 30px;
            cursor: pointer;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        #menu-toggle {
            display: none;
        }

        .menu-button,
        .menu-button::before,
        .menu-button::after {
            display: block;
            background-color: black;
            position: absolute;
            height: 4px;
            width: 30px;
            transition: transform 400ms cubic-bezier(0.23, 1, 0.32, 1);
            border-radius: 2px;
        }

            .menu-button::before {
                content: '';
                margin-top: -8px;
            }

            .menu-button::after {
                content: '';
                margin-top: 8px;
            }

        #menu-toggle:checked + .menu-button-container .menu-button::before {
            margin-top: 0px;
            transform: rotate(405deg);
        }

        #menu-toggle:checked + .menu-button-container .menu-button {
            background: rgba(255, 255, 255, 0);
        }

            #menu-toggle:checked + .menu-button-container .menu-button::after {
                margin-top: 0px;
                transform: rotate(-405deg);
            }


        .infobox > * {
            margin-bottom: 5px; /* set the margin between child elements, except for the last child */
        }

    </style>

</head>
<body>




    <div class="header" style="width: 100%; height: 100px; background-color: #2b2e2f;position:relative">
        <div style="height: 100%; display: inline"><img style="height:100%;" src="~/Images/Pocketbeat-onblack-transparent-new.png" /></div>
        <div style="display:inline;position:absolute;top: 45px;left: 292px;font-size: x-large;color: white;">admin</div>

        <div style="color:white;vertical-align: central; display: flex; align-items: center; height: 100%; position: absolute; right: 10px; top:0">
            @username
        </div>

    </div>

    <table height="100%" width="100%">
        <tr>
            <td style="vertical-align: top; background-color: #2b2e2f; width: 20%">
                <div id="side_nav_bar">
                    <div class="admin-menu" style="list-style: none;">
                        <li><a href="@Url.Action("Index","AdminVideos")">Videos</li>
                        <li><a href="@Url.Action("Index","AdminFans")">Fans</a></li>
                        <li><a href="@Url.Action("Index","AdminArtists")">Artists</a></li>

                    </div>

                </div>
            </td>
            <td style="vertical-align:top;padding:30px">
                <div>
                    @RenderBody()
                </div>
            </td>
        </tr>


    </table>





    <script src="~/js/site.js" asp-append-version="true"></script>

    <script>


        //create a function which changes tha backgroud color of the clicked li element to red and the rest to blue
        function changeColor() {
            //get all the li elements
            var liElements = document.querySelectorAll("li");
            //loop through the li elements
            for (var i = 0; i < liElements.length; i++) {
                //add an event listener to each li element
                liElements[i].addEventListener("click", function () {
                    console.log("clicked");
                    //loop through the li elements again
                    for (var j = 0; j < liElements.length; j++) {
                        //change the background color of each li element to blue
                        liElements[j].style.backgroundColor = "#2b2e2f";
                    }
                    //change the background color of the clicked li element to red
                    this.style.backgroundColor = "black";
                })
            }
        }

        changeColor();

    </script>


    @RenderSection("Scripts", required: false)
</body>
</html>
