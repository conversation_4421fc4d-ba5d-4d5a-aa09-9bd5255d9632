﻿.container1 {
}


.video-container {
    position: relative;
    padding-bottom: 56.25%;
    /* 16:9 */
    height: 0;
}

    .video-container iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }


.button1 {
    width: 100%;
    display: block;
    margin: auto;
    background-color: white;
    text-size-adjust: auto;
}

    .button1:hover {
        background-color: lightgray;
    }


.section {
    /*margin-bottom: 3vw;*/
}

.streaming_services {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 4vw;
    gap: 4vw;
    justify-content: flex-start;
}



.button1 {
    width: 100%;
    display: block;
    margin: auto;
    background-color: white;
    text-size-adjust: auto;
}

.streaming_services .straming_service_icon {
    width: 35px;
    height: 35px;
}

@media screen and (max-width: 584px) {

    .album_image {
        height: 40vw;
    }

}

@media screen and (min-width: 585px) {

    .album_image {
        height: 16vw;
    }
}





.style_image {
    display: inline;
    height: 100%;
}


/*video title*/
@media screen and (max-width: 584px) {

    .video-title {
        font-size: 4.3vw;
        text-align: center;
        font-weight: 900;
    }
}

@media screen and (min-width: 585px) {

    .video-title {
        font-size: 1.3vw;
        text-align: center;
        padding: 1vw;
        font-weight: 900;
    }
}

/*description*/

/*@media screen and (min-width: 585px) {

    .DescriptionTruncated {
        width: 100%;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        color: black;
        font-size: 1vw;
        margin: auto;
        margin-top: 1vw;
        margin-bottom: 1vw;
        text-align: left;
    }

    .DescriptionExtended {
        width: 100%;
        display: none;
        font-size: 1vw;
        margin: auto;
        margin-top: 1vw;
        margin-bottom: 1vw;
        text-align: left;
    }
}

@media screen and (max-width: 584px) {

    .DescriptionTruncated {
        width: 100%;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        color: black;
        font-size: 3vw;
        margin: auto;
        margin-top: 1vw;
        margin-bottom: 1vw;
        text-align: left;
    }

    .DescriptionExtended {
        width: 100%;
        display: none;
        font-size: 1vw;
        margin: auto;
        margin-top: 1vw;
        margin-bottom: 1vw;
        text-align: left;
    }
}*/



/*section titile*/

@media screen and (min-width: 585px) {
    .section_title {
        text-align: left;
        margin-top: 2vw;
        margin-bottom: 1vw;
        font-weight: 900;
        font-size: 1.3vw;
    }
}

@media screen and (max-width: 584px) {
    .section_title {
        text-align: left;
        margin-top: 8vw;
        margin-bottom: 2vw;
        font-weight: 900;
        font-size: 3.5vw;
    }
}











