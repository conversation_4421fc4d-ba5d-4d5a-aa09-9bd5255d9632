﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Pocketbeat_mvc.Models;
using Pocketbeat_mvc.ViewModels;
using System.Net.WebSockets;

namespace Pocketbeat_mvc.Controllers
{
    public class AdminVideosController : Controller
    {

        private readonly IWebHostEnvironment _env;

        public AdminVideosController(IWebHostEnvironment env)
        {
            _env = env;
        }


        public IActionResult Index()
        {
            AdminVideosViewModel vm = new AdminVideosViewModel();

            using (Data data = new Data())
            {
                vm.AllArtists = data.Artists.ToList();
                //vm.AllGenres = data.Genres.ToList();
                vm.AddSectionVisible = false;
                vm.Video = new Video();
            }

            return View(vm);
        }


        public IActionResult NewAddSection()
        {
            AdminVideosViewModel vm = new AdminVideosViewModel();

            using (Data data = new Data())
            {
                vm.AllArtists = data.Artists.ToList();
                //  vm.AllGenres = data.Genres.ToList();
                vm.AddSectionVisible = true;
                vm.Video = new Video();
            }

            return View("index", vm);
        }


        public async Task<IActionResult> SaveAsync(AdminVideosViewModel vm, IFormFile pic_vertical_upload, IFormFile pic_horizontal_upload, IFormFile pic_square_upload)
        {
            using (Data data = new Data())
            {

                //request form data
                var relatedArtists = Request.Form["select_related_artists"];
                foreach (var relatedArtistIdString in relatedArtists)
                {
                    int artistId = int.Parse(relatedArtistIdString);
                    var relatedArtist = data.Artists.First(t => t.Id == artistId);
                    vm.Video.RelatedArtists.Add(relatedArtist);

                }



                vm.Video.CreatedDate = DateTime.Now;
                vm.Video.VerticalImage = saveIFromImage(pic_vertical_upload);
                vm.Video.VerticalImageSmall = await saveIFormImageScaledAsync(pic_vertical_upload);
                vm.Video.HorizontalImage = saveIFromImage(pic_horizontal_upload);
                vm.Video.HorizontalImageSmall = await saveIFormImageScaledAsync(pic_horizontal_upload);
                vm.Video.SquareImage = saveIFromImage(pic_square_upload);
                vm.Video.SquareImageSmall = await saveIFormImageScaledAsync(pic_square_upload);
                data.Videos.Add(vm.Video);
                data.SaveChanges();

                AdminVideosViewModel vmNew = new AdminVideosViewModel();
                vmNew.AllArtists = data.Artists.ToList();
                //vmNew.AllGenres = data.Genres.ToList();
                vmNew.AddSectionVisible = false;
                vmNew.Video = new Video();

                return View("Index", vmNew);

            }
        }


        public IActionResult DeleteVideo(int video_id)
        {


            using (Data data = new Data())
            {
                if (video_id != 0)
                {
                    var video = data.Videos.First(v => v.Id == video_id);
                    data.Videos.Remove(video);
                    data.SaveChanges();
                }

                AdminVideosViewModel vm = new AdminVideosViewModel();

                vm.AllArtists = data.Artists.ToList();
                //vm.AllGenres = data.Genres.ToList();
                vm.AddSectionVisible = false;
                vm.Video = new Video();

                return View("Index", vm);

            }


        }

        string saveIFromImage(IFormFile formFile)
        {
            if (formFile != null && formFile.Length > 0)
            {
                // Get the file extension
                string fileExtension = Path.GetExtension(formFile.FileName);



                // Generate a unique file name
                string fileName = Guid.NewGuid().ToString() + fileExtension;

                fileName = fileName.Replace(' ', '_');

                // Get the path to the uploads folder
                string uploadsFolder = Path.Combine(_env.WebRootPath, "Uploads");

                // Combine the file name and the uploads folder to get the full path
                string filePath = Path.Combine(uploadsFolder, fileName);

                // Save the file to the server
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    formFile.CopyTo(fileStream);
                }

                return fileName;
            }
            else
            {
                return "";
            }

        }


        async Task<string> saveIFormImageScaledAsync(IFormFile formFile)
        {
            // Resize the image to a specific resolution (e.g., 800x600)
            using (var image = SixLabors.ImageSharp.Image.Load(formFile.OpenReadStream()))
            {
                var resizedImage = image.Clone(ctx => ctx.Resize(image.Width / 3, image.Height / 3));
                var newFileName = $"{Guid.NewGuid().ToString()}.jpg"; // Generate a unique filename
                var uploadPath = Path.Combine(_env.WebRootPath, "Uploads");
                var fullPath = Path.Combine(uploadPath, newFileName);

                // Save the resized image to the disk
                using (var stream = new FileStream(fullPath, FileMode.Create))
                {
                    await resizedImage.SaveAsJpegAsync(stream);
                }
                return newFileName;
            }
        }
    }
}
