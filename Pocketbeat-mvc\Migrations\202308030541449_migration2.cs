﻿namespace Pocketbeat_mvc.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class migration2 : DbMigration
    {
        public override void Up()
        {
            DropForeignKey("dbo.Genres", "Video_Id", "dbo.Videos");
            DropIndex("dbo.Genres", new[] { "Video_Id" });
            CreateTable(
                "dbo.GenreVideos",
                c => new
                    {
                        Genre_Id = c.Int(nullable: false),
                        Video_Id = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Genre_Id, t.Video_Id })
                .ForeignKey("dbo.Genres", t => t.Genre_Id, cascadeDelete: true)
                .ForeignKey("dbo.Videos", t => t.Video_Id, cascadeDelete: true)
                .Index(t => t.Genre_Id)
                .Index(t => t.Video_Id);
            
            DropColumn("dbo.Genres", "Video_Id");
        }
        
        public override void Down()
        {
            AddColumn("dbo.Genres", "Video_Id", c => c.Int());
            DropForeignKey("dbo.GenreVideos", "Video_Id", "dbo.Videos");
            DropForeignKey("dbo.GenreVideos", "Genre_Id", "dbo.Genres");
            DropIndex("dbo.GenreVideos", new[] { "Video_Id" });
            DropIndex("dbo.GenreVideos", new[] { "Genre_Id" });
            DropTable("dbo.GenreVideos");
            CreateIndex("dbo.Genres", "Video_Id");
            AddForeignKey("dbo.Genres", "Video_Id", "dbo.Videos", "Id");
        }
    }
}
