﻿using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Mvc;
using Pocketbeat_mvc.Models;
using Pocketbeat_mvc.ViewModels;
using System.Diagnostics;

namespace Pocketbeat_mvc.Controllers
{
    public class HomeController : Controller
    {
        private readonly ILogger<HomeController> _logger;

        public HomeController(ILogger<HomeController> logger)
        {
            _logger = logger;
        }

        public IActionResult Index()
        {
            //temp  
            //using (Data data = new Data())
            //{
            //    var allvids = data.Videos.ToList();

            //    foreach (var vid in allvids)
            //    {
            //        var temp1 = vid.HorizontalImage;
            //        var temp2 = vid.HorizontalImageSmall;

            //        vid.HorizontalImage = vid.VerticalImage;
            //        vid.HorizontalImageSmall = vid.VerticalImageSmall;

            //        vid.VerticalImage = temp1;
            //        vid.VerticalImageSmall = temp2;

            //        data.SaveChanges();
            //    }
            //}
            //

            return View();
        }

        //GetDynamicHtml
        public ActionResult GetDynamicHtml(int size)
        {
            return Meth(size, "");
            
        }

        public ActionResult GetDynamicHtml2(int size, string query)
        {
            return Meth(size, query);
        }



        public ActionResult Meth(int size , string query)
        {
            int noOfCols = 0;


            if (size > 0 && size <= 480)
            {

                noOfCols = 2;
            }
            else if (size > 480 && size <= 699)
            {
                noOfCols = 3;
            }
            else if (size > 699 && size <= 840)
            {
                noOfCols = 4;
            }
            else if (size > 840)
            {
                noOfCols = 6;
            }


            PartialViewModel partialViewModel = new PartialViewModel();
            partialViewModel.NoOfColumns = noOfCols;
            partialViewModel.SearchQuery = query;

            if (noOfCols == 2)
            {
                return PartialView("_Col2", partialViewModel);
            }
            else if (noOfCols == 3)
            {
                return PartialView("_Col3New", partialViewModel);
            }
            else if (noOfCols == 4)
            {
                return PartialView("_Col4New3", partialViewModel);
            }
            else if (noOfCols == 5)
            {
                return PartialView("_Col5", partialViewModel);
            }
            else
            {
                return PartialView("_Col6New", partialViewModel);
            }
        }



        

        public IActionResult Privacy()
        {
            return View();
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }


        [HttpPost]
        public bool EmailExists(string param)
        {
            using (Data data = new Data())
            {
                if (data.Emails.Any(e => e.EmailAddress == param.Trim()))
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
        }

        public class MyDataModel
        {
            public string email { get; set; }
            public string country { get; set; }
        }


        [HttpPost]
        public bool SaveData([FromBody] MyDataModel datamodel)
        {
            try
            {
                using (Data data = new Data())
                {
                    //if datamodel.country is null of empty
                    //its a submission from the normal page ( not the prompt)
                    if (string.IsNullOrEmpty(datamodel.country))
                    {
                        data.Emails.Add(new Email() { EmailAddress = datamodel.email.Trim(), SavedTime = DateTime.Now });
                        data.SaveChanges();
                    }
                    else //we come to here only if datamode.country 
                         //is not not null or empty which can only happen when its
                         //submitted from the prompt
                    {
                        if (EmailExists(datamodel.email))
                        {
                            var existingEmail = data.Emails.Where(e => e.EmailAddress == datamodel.email.Trim()).FirstOrDefault();
                            existingEmail.Country = datamodel.country.Trim();
                            data.SaveChanges();
                        }
                        else
                        {
                            data.Emails.Add(new Email() { EmailAddress = datamodel.email.Trim(), Country = datamodel.country, SavedTime = DateTime.Now });
                            data.SaveChanges();
                        }
                    }
                    return true;
                }
            }
            catch (Exception ex)
            {
                return false;
            }
        }

    }
}