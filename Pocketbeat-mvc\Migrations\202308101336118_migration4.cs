﻿namespace Pocketbeat_mvc.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class migration4 : DbMigration
    {
        public override void Up()
        {
            DropForeignKey("dbo.GenreVideos", "Genre_Id", "dbo.Genres");
            DropForeignKey("dbo.GenreVideos", "Video_Id", "dbo.Videos");
            DropIndex("dbo.GenreVideos", new[] { "Genre_Id" });
            DropIndex("dbo.GenreVideos", new[] { "Video_Id" });
            AddColumn("dbo.Videos", "Genres", c => c.String());
            DropTable("dbo.Genres");
            DropTable("dbo.GenreVideos");
        }
        
        public override void Down()
        {
            CreateTable(
                "dbo.GenreVideos",
                c => new
                    {
                        Genre_Id = c.Int(nullable: false),
                        Video_Id = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Genre_Id, t.Video_Id });
            
            CreateTable(
                "dbo.Genres",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                    })
                .PrimaryKey(t => t.Id);
            
            DropColumn("dbo.Videos", "Genres");
            CreateIndex("dbo.GenreVideos", "Video_Id");
            CreateIndex("dbo.GenreVideos", "Genre_Id");
            AddForeignKey("dbo.GenreVideos", "Video_Id", "dbo.Videos", "Id", cascadeDelete: true);
            AddForeignKey("dbo.GenreVideos", "Genre_Id", "dbo.Genres", "Id", cascadeDelete: true);
        }
    }
}
