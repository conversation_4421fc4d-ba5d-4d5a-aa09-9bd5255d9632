﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Target" xml:space="preserve">
    <value>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</value>
  </data>
  <data name="DefaultSchema" xml:space="preserve">
    <value>dbo</value>
  </data>
</root>