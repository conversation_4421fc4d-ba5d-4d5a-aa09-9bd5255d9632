﻿using Microsoft.AspNetCore.Mvc;
using Pocketbeat_mvc.Models;
using System.Text;

namespace Pocketbeat_mvc.Controllers
{
    public class AdminFansController : Controller
    {
        public IActionResult Index()
        {
            return View();
        }

        public IActionResult DownloadFile(string listType)
        {

            try
            {

                // Create the file content
                string fileContent = "";

                if (listType == "Full_email")
                {
                    using (Data data = new Data())
                    {
                        var emails = data.Emails.Select(e => e.EmailAddress).ToList();

                        foreach (var email in emails)
                        {
                            fileContent += email;
                            fileContent += "\n";
                        }
                    }
                }
                else if (listType == "Full_country")
                {
                    using (Data data = new Data())
                    {
                        var groups = data.Emails
                            .GroupBy(e => e.Country)
                            .Select(g => new { Country = g.Key, Count = g.Count() })
                            .OrderByDescending(g => g.Count)
                            .Where(g => g.Count >= 1)
                            .ToList();

                        foreach (var rec in groups)
                        {
                            fileContent += $"{rec.Country} , {rec.Count}";
                            fileContent += "\n";
                        }
                    }
                }
                else if (listType == "Add_ons")
                {
                    var lastDownloadedDate = AppInfo.GetValue("last_downloaded_date");

                    using (Data data = new Data())
                    {
                        if (lastDownloadedDate == DateTime.MinValue)
                        {
                            var emails = data.Emails.Select(e => e.EmailAddress).ToList();

                            foreach (var email in emails)
                            {
                                fileContent += email;
                                fileContent += "\n";
                            }
                        }
                        else
                        {
                            var list = data.Emails.Where(e => e.SavedTime > lastDownloadedDate).ToList();

                            foreach (var email in list)
                            {
                                fileContent += email.EmailAddress;
                                fileContent += "\n";
                            }
                        }

                    }


                    AppInfo.SetValue("last_downloaded_date", DateTime.Now);
                }


                // Create the file name and file type
                string fileName = "full_list.txt";
                string fileType = "text/plain";

                // Convert the file content to a byte array
                byte[] fileBytes = Encoding.ASCII.GetBytes(fileContent);

                // Return the file as a download
                return File(fileBytes, fileType, fileName);
            }
            catch (Exception ex)
            {
                return NotFound();
            }

        }
    }
}
