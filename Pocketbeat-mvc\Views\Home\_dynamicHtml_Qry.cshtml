﻿@model Pocketbeat_mvc.ViewModels.WidthAndQryPartilViewModel

@{

    List<Video> videos = null;
    List<Video> selectedVideos = new List<Video>();
    using (Data data = new Data())
    {
        videos = data.Videos.Include("RelatedArtists").ToList();

        foreach(var video in videos)
        {
            if (video.VideoTitle.Contains(Model.Query) || video.Genres.Contains(Model.Query) || video.RelatedArtists.Any(a => a.ArtistName.Contains(Model.Query)))
            {
                selectedVideos.Add(video);
            }
        }

        selectedVideos = selectedVideos.OrderByDescending(v => v.CreatedDate).ToList();
    }
}


