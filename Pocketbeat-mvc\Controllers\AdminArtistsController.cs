﻿using Microsoft.AspNetCore.Mvc;
using Pocketbeat_mvc.Models;
using Pocketbeat_mvc.ViewModels;
using static Pocketbeat_mvc.Controllers.HomeController;

namespace Pocketbeat_mvc.Controllers
{
    public class AdminArtistsController : Controller
    {
        public IActionResult Index()
        {
            AdminArtistsViewModel adminArtistsViewModel = new AdminArtistsViewModel();
            return View(adminArtistsViewModel);
        }

        public IActionResult Save(AdminArtistsViewModel adminArtistsViewModel)
        {
            using (Data data = new Data())
            {
                data.Artists.Add(new Artist() { ArtistName = adminArtistsViewModel.Name });
                data.SaveChanges();


                return RedirectToAction("Index");

            }
        }
    }
}
