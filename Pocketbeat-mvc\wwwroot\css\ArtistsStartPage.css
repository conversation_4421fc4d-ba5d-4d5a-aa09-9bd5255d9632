﻿

@media screen and (max-width: 584px) {
    #p_introduction {
        font-size: 3vw;
        font-weight: 600;
    }
}

@media screen and (min-width: 585px) {
    #p_introduction {
        font-size: 1.2vw;
        font-weight: 600;
    }
}

@media screen and (max-width: 584px) {
    .profile_box_container {
        width: 100%;
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
    }

    .profile_box {
        width: 66vw;
        height: 105vw;
        display: inline-block;
        text-align: center;
    }

        .profile_box img {
            height: 84%;
            width: 100%;
            cursor: pointer;
            transition: all 0.5s ease;
        }

            .profile_box img:hover {
                transform: scale(1.1);
            }

        .profile_box .artist_name {
            width: 100%;
            text-align: center;
            color: black;
            margin-bottom: 3px;
            display: block;
            font-size: 4vw;
            font-weight: 600;
        }

        .profile_box .state {
            border: solid;
            color: white;
            display: block;
        }
}

@media screen and (min-width: 585px) {
    .profile_box_container {
        width: 100%;
        display: flex;
        justify-content: start;
        flex-wrap: wrap;
    }

    .profile_box {
        width: 18vw;
        height: 25vw;
        display: inline-block;
        text-align: left;
    }

        .profile_box img {
            height: 83%;
            width: 90%;
            cursor: pointer;
            transition: all 0.5s ease;
        }

            .profile_box img:hover {
                transform: scale(1.1);
            }

        .profile_box .artist_name {
            width: 18vw;
            text-align: center;
            color: black;
            margin-bottom: 3px;
            display: block;
            font-size: 1.2vw;
        }

        .profile_box .state {
            border: solid;
            color: white;
            display: block;
        }
}
