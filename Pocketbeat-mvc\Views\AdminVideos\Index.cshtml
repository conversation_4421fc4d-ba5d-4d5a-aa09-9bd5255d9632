﻿@model Pocketbeat_mvc.ViewModels.AdminVideosViewModel
@using System.IO

@{
    ViewData["Title"] = "Index";
}

@{
    Layout = "~/Views/Shared/_adminPagesLayout.cshtml";
}


@section Styles{

    <link rel="stylesheet" href="~/css/chosen.min.css" />
    <link rel="stylesheet" href="~/css/AdminVideos_v2.css" />

}


@{

    var add_entity_style = (Model.AddSectionVisible == true) ? "display:block" : "display:none";
}


<div class="container">
    <div style="/*margin-left: 50px; */ margin-bottom: 20px">
        <label id="lbl_entity">Videos</label>
        <input type="button" onclick="location.href='@Url.Action("NewAddSection", "AdminVideos")'" class="btn1" value="Add Video" id="btn_add_videos" />

    </div>

    @if (Model.AddSectionVisible == false)
    {
        using (Data data = new Data())
        {
            var videos = data.Videos.Include("RelatedArtists").ToList();

            videos = videos.OrderByDescending(v => v.CreatedDate).ToList();

            foreach (var video in videos)
            {
                <div id="@video.Id" draggable="true" ondrop="drop(event)" ondragover="allowDrop(event)" ondragstart="drag(event)" class="bar_video" @*onclick="location.href='@Url.Action("EditMusicEditTrack", "AdminMusic", new { music_id = Model.Music.Id, track_id = track.Id })'"*@>

                    <div id="@video.Id" class="video_title">@video.VideoTitle</div>


                    @if (video.RelatedArtists.Count > 1)
                    {
                        <div id="@video.Id" class="artist_name">@video.RelatedArtists[0].ArtistName X @video.RelatedArtists[1].ArtistName</div>
                    }
                    else if (video.RelatedArtists.Count == 1)
                    {
                        <div id="@video.Id" class="artist_name">@video.RelatedArtists[0].ArtistName</div>
                    }

                    <div id="@video.Id" class="video_ceation_date" >@video.CreatedDate.ToString("yyyy-MM-dd")</div>

                    <input id="@video.Id" type="button" value="Delete" onclick="event.stopPropagation(); confirmVideoDelete('@video.Id');">
                </div>
            }
        }


    }




    <div style=@add_entity_style id="add_entity">
        <button id="add_entity_close_btn" class="close-button">X</button>

        <div style="/*margin-left: 50px; */ margin-bottom: 20px; font-size: large; color: white;">
            Video
        </div>

        <form method="post" id="form" enctype="multipart/form-data">






            <div class="infobox">
                <label>Youtube Link</label>
                @Html.TextBoxFor(m => m.Video.YoutubeLink)
            </div>

            <div class="infobox">
                <label>Video Title</label>
                @Html.TextBoxFor(m => m.Video.VideoTitle)
            </div>


            <div class="infobox" id="infobox_related_artists">
                <label>Artist</label>
                <select style="display:none" class="hidden_select">
                    @foreach (var option in Model.AllArtists)
                    {
                        @Html.Raw($"<option value='{option.Id}'>{option.ArtistName}</option>")
                    }

                </select>

            </div>
            <label id="add_another_related_artist" class="add_another">Add Another Artist</label>


            <div class="infobox" id="infobox_related_genres">
                <label>Genres</label>
                @Html.TextBoxFor(m => m.Video.Genres)


            </div>

            <div>
                @Html.CheckBoxFor(m => m.Video.TopPosition) <span style="color:white">Top Position?</span>
            </div>



            <div id="pic_vertical" onclick="pic_vertical_upload.click();">
                <label style="position: relative; top: 5px; left: 5px;">1920x1080</label>
                <div class="plus">+</div>
            </div>
            <input id='pic_vertical_upload' name="pic_vertical_upload" type='file' value="" style='display: none' accept='.jpg' />


            <div id="pic_horizontal" onclick="pic_horizontal_upload.click();">
                <label style="position: relative; top: 5px; left: 5px;">1080x1920</label>
                <div class="plus">+</div>
            </div>
            <input id='pic_horizontal_upload' name="pic_horizontal_upload" type='file' value="" style='display: none' accept='.jpg' />


            <div id="pic_square" onclick="pic_square_upload.click();">
                <label style="position: relative; top: 5px; left: 5px;">1080x1080</label>
                <div class="plus">+</div>
            </div>
            <input id='pic_square_upload' name="pic_square_upload" type='file' value="" style='display: none' accept='.jpg' />


            <div class="button_container">
                <input type="submit" formaction="@Url.Action("Save","AdminVideos")" value="Publish Video" class="btn1" />
            </div>

        </form>


    </div>


</div>


@section Scripts{




    <script src="~/js/chosen.jquery.min.js"></script>


    <script>

        var _hor_image = null;
        var _ver_image = null;
        var _sqr_image = null;

        var test1 = [];
        var videoss = "";
        var e = "";

        var mouse_focused_related_select_element_id = null;


        var LastRelatedTrackSelectElementPostFix = 0;
        var LastRelatedVidoeSelectElementPostFix = 0;
        var LastRelatedStyleSelectElementPostFix = 0;
        var LastRelatedJournalSelectElementPostFix = 0;


        document.onkeyup = function (e) {
            e = e || window.event;
            if (e.keyCode == "8") {


                if (mouse_focused_related_select_element_id != null) {
                    document.getElementById(mouse_focused_related_select_element_id).remove();
                    document.getElementById(mouse_focused_related_select_element_id.slice(0, -7)).remove();
                }

            }
        };

        function confirmVideoDelete(v_id) {
            if (confirm("Are you sure you want to delete this?")) {
                window.location.href = "/AdminVideos/DeleteVideo?video_id=" + v_id;
            }
        }

        function f3(infoboxElementId, selectElementName, clickingElementId) { //"clickingElement" could be a button

            document.getElementById(clickingElementId).addEventListener("click", function () {

                var select = document.createElement("select");
                select.name = selectElementName;
                var randLetter = String.fromCharCode(65 + Math.floor(Math.random() * 26));
                var uniqid = randLetter + Date.now();
                select.id = uniqid;
                select.style.display = "inline";





                var hiddenselect = document.getElementById(infoboxElementId).querySelector(".hidden_select");
                for (var i = 0; i < hiddenselect.options.length; i++) {
                    var option = hiddenselect.options[i];
                    select.add(new Option(option.text, option.value));
                }

                document.getElementById(infoboxElementId).appendChild(select);

                $("#" + uniqid).chosen();


                //id of the generated element for the hidden select element

                var generated_element_id = uniqid + "_chosen";

                var generated_element = document.getElementById(generated_element_id);


                generated_element.addEventListener("mouseover", function () {

                    mouse_focused_related_select_element_id = generated_element.id;
                    console.log("mouse over");
                    console.log(mouse_focused_related_select_element_id);

                });

                generated_element.addEventListener("mouseout", function () {

                    mouse_focused_related_select_element_id = null;
                    console.log("mouse out");
                });


            });



        }


        f3("infobox_related_artists", "select_related_artists", "add_another_related_artist");








        //when we input an id of a .infobox
        //this returns a collection of objects
        //containing id and text properties
        function f2(infobox_id) {

            const infobox = document.getElementById(infobox_id);
            const selectElements = infobox.querySelectorAll('select');


            let retobjs = [];

            for (const select of selectElements) {
                const selectedIndex = select.selectedIndex;
                const selectedOption = select.options[selectedIndex];
                const selectedText = selectedOption.text;
                const selectedValue = selectedOption.value;

                var obj = Object.assign({}, { id: selectedValue, text: selectedText });
                retobjs.push(obj);

            }

            return retobjs;

        }







        document.getElementById("pic_horizontal_upload").addEventListener("change", hor_image_upload, false);
        document.getElementById("pic_vertical_upload").addEventListener("change", ver_image_upload, false);
        document.getElementById("pic_square_upload").addEventListener("change", sqr_image_upload, false);
        /*document.getElementById("btn_save").addEventListener("click", btn_save_click);*/
        //document.getElementById("btn_add_artist").addEventListener("click", function () {

        //    document.getElementById("add_artist").style.display = "block";

        //});

        document.getElementById("add_entity_close_btn").addEventListener("click", function () {


            //document.getElementById("add_entity").style.display = "none";

            window.location.href = "/AdminVideos/Index";
            //make element vlaues defualt

        });

        function refresh_profile_container() {

            //clear the inside of profile_container
            //get profile data from backend using ajax
        }


        function sqr_image_upload() {

            var fileInput = document.getElementById('pic_square_upload');
            var file = fileInput.files[0];
            _sqr_image = file;
            var fileUrl = URL.createObjectURL(file);

            var div = document.getElementById('pic_square');
            div.style.backgroundImage = 'url(' + fileUrl + ')';
        }


        function ver_image_upload() {

            var fileInput = document.getElementById('pic_vertical_upload');
            var file = fileInput.files[0];
            _ver_image = file;
            var fileUrl = URL.createObjectURL(file);

            var div = document.getElementById('pic_vertical');
            div.style.backgroundImage = 'url(' + fileUrl + ')';
        }

        function hor_image_upload() {

            var fileInput = document.getElementById('pic_horizontal_upload');
            var file = fileInput.files[0];
            _hor_image = file;
            var fileUrl = URL.createObjectURL(file);

            var div = document.getElementById('pic_horizontal');
            div.style.backgroundImage = 'url(' + fileUrl + ')';

        }

        function SaveImageAndReturnGuid(file) {

            var guid = null;


            var formData = new FormData();
            formData.append("file", file);

            $.ajax({
                type: "POST",
                url: "SaveImage.ashx",
                data: formData,
                contentType: false,
                processData: false,
                async: false,
                success: function (response) {
                    guid = response;
                },
                error: function (ex) {
                    test1 = ex;
                }
            });


            return guid;
        }








        function validationForm() {




        }


        function confirmDelete() {

            if (confirm("Are you sure you want to delete this?")) {
                window.location.href = "/AdminVideos/DeleteVideo?video_id=@Model.Video.Id";
            }
        }


    </script>

    <script type="text/javascript">

        var mouse_focused_related_select_element_id = null;

        window.onload = function () {


            $('select:not(.hidden_select)').each(function () {

                var randLetter = String.fromCharCode(65 + Math.floor(Math.random() * 26));
                var uniqid = randLetter + Date.now();


                $(this).attr('id', uniqid);

                $(this).chosen();

                var generated_element_id = uniqid + "_chosen";

                //$(this).on('mouseover', function () {
                //    mouse_focused_related_select_element_id = $(this).attr('id');
                //    console.log('mouse over');
                //    console.log(mouseFocusedRelatedSelectElementId);
                //});


                //$(this).on("mouseout", function () {

                //    mouse_focused_related_select_element_id = null;
                //    console.log("mouse out");
                //});

                document.getElementById(generated_element_id).addEventListener("mouseover", function () {

                    mouse_focused_related_select_element_id = generated_element_id;
                    console.log("mouse over");
                    console.log(mouse_focused_related_select_element_id);

                });


                document.getElementById(generated_element_id).addEventListener("mouseout", function () {

                    mouse_focused_related_select_element_id = null;
                    console.log("mouse out");
                });


            });
        }

    </script>

    <script>

        var newTab = null;

        $(document).ready(function () {
            $("#preview_button").click(function (e) {
                e.preventDefault();

                var form = $("#form");
                var formData = new FormData(form[0]);

                $.ajax({
                    type: "POST",
                    url: "@Url.Action("Preview","Video")",
                    data: formData,
                    async: false,
                    contentType: false,
                    processData: false,
                    success: function (result) {

                        console.log(result);

                        newTab = window.open();
                        newTab.document.write(result);
                    }
                });
            });
        });
    </script>


}


