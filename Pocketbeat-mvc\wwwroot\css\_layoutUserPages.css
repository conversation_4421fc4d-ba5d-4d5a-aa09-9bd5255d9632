﻿/*footer menu*/
@media screen and (max-width: 584px){
    .footer_menu_container {
        display:block;
    }
}

@media screen and (min-width: 585px) {
    .footer_menu_container {
        display: flex;
        justify-content: space-evenly;
    }
}


    /*menu list elements*/
    @media screen and (max-width: 584px) {
        #menu-toggle ~ .menu .li_div {
            height: 0;
            margin: 0;
            padding: 0;
            border: 0;
            transition: height 400ms cubic-bezier(0.23, 1, 0.32, 1);
        }

        #menu-toggle:checked ~ .menu .li_div {
            height: 12vw;
            padding: 0;
        }


        .menu-flex-parent {
            display: flex;
            justify-content: flex-start;
            flex-wrap: wrap;
        }

        .menu-flex-child {
            width: 8vw;
            height: 8vw;
            padding: 3vw;
        }

        .menu-flex-image {
            width: 100%;
            height: 100%;
            position: static;
            transform: none;
        }
    }

    @media screen and (min-width: 585px) {

        #menu-toggle ~ .menu .li_div {
            height: 0;
            margin: 0;
            padding: 0;
            border: 0;
            transition: height 400ms cubic-bezier(0.23, 1, 0.32, 1);
        }

        #menu-toggle:checked ~ .menu .li_div {
            height: 4vw;
            padding: 0;
        }


        .menu-flex-parent {
            display: flex;
            justify-content: flex-start;
            flex-wrap: wrap;
        }

        .menu-flex-child {
            width: 3vw;
            height: 3vw;
            padding: 1vw;
        }

        .menu-flex-image {
            width: 100%;
            height: 100%;
            position: static;
            transform: none;
        }
    }



    /*related styles*/
    @media screen and (max-width: 584px) {
        .related_styles {
            height: 44vw;
            text-align: left;
            overflow-x: auto;
            overflow-y: hidden;
            white-space: nowrap;
            font-size: 0;
        }

            .related_styles .style_image {
                display: inline;
                height: 100%;
            }
    }

    @media screen and (min-width: 585px) {

        .related_styles {
            height: 16vw;
            text-align: left;
            overflow-x: auto;
            overflow-y: hidden;
            white-space: nowrap;
        }

            .related_styles .style_image {
                display: inline;
                height: 100%;
                margin-right: -0.3vw;
            }
    }

    .section {
        margin-bottom: 3vw;
    }

    /*social media icons*/
    @media screen and (max-width: 584px) {

        .icons_align {
            margin-bottom: -1vw;
            margin-top: -1vw;
        }


        #div_social_media_icons {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
            /*margin-top: 6vw;*/
        }

        #div_social_media_icons_new {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
            margin-top: -2vw;

        }

        .div_single_icon {
            width: 14vw;
            height: 14vw;
            margin: 3vw;
            margin-bottom: 0vw;
        }

            .div_single_icon img {
                width: 100%;
                height: 100%;
            }
    }

    @media screen and (min-width: 585px) {

        .icons_align {
            margin-bottom: -4vw;
        }

        #div_social_media_icons {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
        }
        #div_social_media_icons {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
        }

        .div_single_icon {
            width: 4.5vw;
            height: 4.5vw;
            padding: 0px;
            margin: 2.5vw;
            margin-bottom : 0vw;
        }

            .div_single_icon img {
                width: 100%;
                height: 100%;
            }
    }


    @media screen and (max-width: 584px) {

        .p_introduction {
            font-size: 3vw;
            margin: 3vw;
            line-height: 1.6;
            text-align: left;
        }
    }

    @media screen and (min-width: 585px) {

        .p_introduction {
            font-size: 1.2vw;
            margin: 3vw;
            line-height: 1.6;
            text-align: left;
        }
    }




    @media screen and (min-width: 585px) {

        .DescriptionTruncated {
            width: 100%;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            color: black;
            font-size: 1.2vw;
            margin: auto;
            margin-top: 1vw;
            margin-bottom: 1vw;
            text-align: left;
        }

        .DescriptionExtended {
            width: 100%;
            display: none;
            font-size: 1.2vw;
            margin: auto;
            margin-top: 1vw;
            margin-bottom: 1vw;
            text-align: left;
        }
    }

    @media screen and (max-width: 584px) {

        .DescriptionTruncated {
            width: 100%;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            color: black;
            font-size: 3vw;
            margin: auto;
            margin-top: 1.2vw;
            margin-bottom: 1vw;
            text-align: left;
        }

        .DescriptionExtended {
            width: 100%;
            display: none;
            font-size: 3vw;
            margin: auto;
            margin-top: 1vw;
            margin-bottom: 1vw;
            text-align: left;
        }
    }





    /*button_type_3*/
    @media screen and (max-width: 584px) {

        .button_type_3 {
            width: 100%;
            margin: auto;
            display: block;
            margin-top: 2vw;
            margin-bottom: 2vw;
            height: 9vw;
            background: white;
            font-size: 3.5vw;
            font-weight: bold;
            cursor: pointer;
            border-left-color: black;
            border-top-color: black;
        }
    }

    @media screen and (min-width: 585px) {

        .button_type_3 {
            width: 100%;
            margin: auto;
            display: block;
            margin-top: 1vw;
            margin-bottom: 2vw;
            height: 3.5vw;
            background: white;
            font-size: 1.2vw;
            font-weight: bold;
            cursor: pointer;
            border-left-color: black;
            border-top-color: black;
        }
    }


    body {
        /* position: relative; */
        /* display: table;*/
    }


    @media screen and (max-width: 584px) {

        header {
            width: 100%;
            height: 10vw;
            position: fixed;
            top: 0px;
            right: 0px;
            left: 0px;
            z-index: 1000;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            color: #FFF;
            background-color: white;
            /* padding: 1em; */
        }

            header img {
                height: 80%;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
            }
    }

    @media screen and (min-width: 585px) {

        header {
            width: 100%;
            height: 5vw;
            position: fixed;
            top: 0px;
            right: 0px;
            left: 0px;
            z-index: 1000;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            color: #FFF;
            background-color: white;
            /* padding: 1em; */
        }

            header img {
                height: 36%;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
            }
    }



    .menu {
        display: flex;
        flex-direction: row;
        list-style-type: none;
        margin: 0;
        padding: 0;
    }

        .menu > li {
            margin: 0 1rem;
            overflow: hidden;
        }

    .menu-button-container {
        display: none;
        height: 100%;
        width: 30px;
        cursor: pointer;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: absolute;
        right: 2vw;
    }

    #menu-toggle {
        display: none;
    }

    .menu-button,
    .menu-button::before,
    .menu-button::after {
        display: block;
        background-color: black;
        position: absolute;
        height: 4px;
        width: 30px;
        transition: transform 400ms cubic-bezier(0.23, 1, 0.32, 1);
        border-radius: 2px;
    }

        .menu-button::before {
            content: '';
            margin-top: -8px;
        }

        .menu-button::after {
            content: '';
            margin-top: 8px;
        }

    #menu-toggle:checked + .menu-button-container .menu-button::before {
        margin-top: 0px;
        transform: rotate(405deg);
    }

    #menu-toggle:checked + .menu-button-container .menu-button {
        background: rgba(255, 255, 255, 0);
    }

        #menu-toggle:checked + .menu-button-container .menu-button::after {
            margin-top: 0px;
            transform: rotate(-405deg);
        }

    @media screen and (min-width: 585px) {
        .menu-button-container {
            display: flex;
        }

        .menu {
            position: absolute;
            top: 0;
            margin-top: 5vw;
            left: 0;
            flex-direction: column;
            width: 100%;
            justify-content: center;
            align-items: center;
        }

        #menu-toggle ~ .menu li {
            height: 0;
            margin: 0;
            padding: 0;
            border: 0;
            transition: height 400ms cubic-bezier(0.23, 1, 0.32, 1);
            cursor: default;
        }

        #menu-toggle:checked ~ .menu li {
            height: 1.5em;
            padding: 0.5em;
            transition: height 400ms cubic-bezier(0.23, 1, 0.32, 1);
            cursor: default;
        }

        .menu > li {
            display: flex;
            justify-content: center;
            margin: 0;
            padding: 0.5em 0;
            width: 100%;
            color: white;
            background-color: black;
        }

            .menu > li:not(:last-child) {
                border-bottom: 1px solid #444;
            }
    }


    @media screen and (max-width: 584px) {
        .menu-button-container {
            display: flex;
        }

        .menu {
            position: absolute;
            top: 0;
            margin-top: 10vw;
            left: 0;
            flex-direction: column;
            width: 100%;
            justify-content: center;
            align-items: center;
        }

        #menu-toggle ~ .menu li {
            height: 0;
            margin: 0;
            padding: 0;
            border: 0;
            transition: height 400ms cubic-bezier(0.23, 1, 0.32, 1);
        }

        #menu-toggle:checked ~ .menu li {
            height: 1.5em;
            padding: 0.5em;
            transition: height 400ms cubic-bezier(0.23, 1, 0.32, 1);
        }

        .menu > li {
            display: flex;
            justify-content: center;
            margin: 0;
            padding: 0.5em 0;
            width: 100%;
            color: white;
            background-color: black;
        }

            .menu > li:not(:last-child) {
                border-bottom: 1px solid #444;
            }
    }

    @media screen and (min-width: 585px) {

        .parent {
            display: grid;
            grid-template-columns: 20% 60% 20%;
            grid-template-rows: auto;
            position: relative;
            top: 5vw;
        }


        .pacman_left {
            grid-column: 1 / 2;
            position: fixed;
            left: 0;
            width: 20%;
            height: 100%;
        }

        .content {
            grid-column: 2 / 3;
        }

        .pacman_right {
            grid-column: 3 / 4;
            position: fixed;
            right: 0;
            width: 20%;
            height: 100%;
        }
    }





    @media screen and (max-width: 584px) {

        .parent {
            display: grid;
            grid-template-columns: 100%;
            grid-template-rows: auto;
            position: relative;
            top: 10vw;
        }

        .pacman_left {
            display: none;
        }

        .pacman_right {
            display: none;
        }

        .content {
            grid-column: 1 / 1;
        }

        #footer_less_584px {
            display: block;
        }
    }





    #pacman_table_left td {
        background-size: cover;
        width: 2vw;
        height: 2vw;
        border: none;
        margin: 0;
        padding: 0;
    }

    #pacman_table_right td {
        background-size: cover;
        width: 2vw;
        height: 2vw;
        border: none;
        margin: 0;
        padding: 0;
    }







    @media screen and (max-width:584px) {

        #single_line {
            display: none;
        }

        #double_line {
            display: inline;
        }



        .logo_container {
            height: 100%;
            display: block;
            margin: auto;
        }
    }

    @media screen and (min-width:585px) {


        #single_line {
            display: inline;
        }

        #double_line {
            display: none;
        }

        .logo_container {
            height: 100%;
            display: block;
            margin: auto;
        }
    }




    .open_mouth {
        background-image: url('/icons/opened.png');
        background-size: cover;
        width: 5vw;
        height: 7vw;
        position: absolute;
        right: -1vw;
        bottom: -1vw;
        z-index: 1;
    }

    .closed_mouth {
        background-image: url('/icons/closed.png');
        background-size: cover;
        width: 5vw;
        height: 7vw;
        position: absolute;
        right: -1vw;
        bottom: -1vw;
        z-index: 1;
    }

    @media screen and (max-width: 584px) {
        .footer {
            text-align: center;
            background-color: black;
            color: white;
            padding: 6vw;
            margin: auto;
            font-size: 3vw;
        }

        .footer_links {
            margin-top: 3vw;
            font-size: 3vw;
            margin-bottom: 3vw;
        }

        .footer_desc {
            text-align: left;
            font-size: 3vw;
        }
    }

    @media screen and (min-width: 585px) {
        .footer {
            text-align: center;
            background-color: black;
            color: white;
            padding: 6vw;
            margin: auto;
            font-size: 1.2vw;
            position: absolute;
            LEFT: 0;
            Z-INDEX: 1;
            padding-left: 22vw;
            padding-right: 22vw;
            padding-top: 6vw;
        }

        .footer_links {
            margin-top: 2vw;
            font-size: 1.2vw;
            margin-bottom: 2vw;
        }

        .footer_desc {
            text-align: left;
            font-size: 1.2vw;
        }
    }


    .trade_name {
    }

    .poweredby {
    }


    @media screen and (min-width:585px) {
        .footer_face {
            width: 4vw;
            margin: auto;
            margin-top: 3vw;
        }
    }

    @media screen and (max-width:584px) {
        .footer_face {
            width: 15vw;
            margin: auto;
            margin-top: 3vw;
        }
    }

    /*three heads*/
    @media screen and (min-width: 585px) {


        .three_heads {
            width: 17%;
            margin: auto;
            margin-top: 5vw;
            margin-bottom: 5vw;
        }
    }

    @media screen and (max-width: 584px) {


        .three_heads {
            width: 26%;
            margin: auto;
            margin-top: 10vw;
            margin-bottom: 10vw;
        }
    }


    /*youtube subscribe button all other elements inside it */
    @media screen and (max-width: 584px) {

        .youtube_subs_button {
            width: 100%;
            height: 9.5vw;
            font-size: 3.2vw;
            font-weight: 600;
            color: black;
            background-color: white;
            border-radius: 10px;
            display: block;
            margin: auto;
            margin-top: 1vw;
            border-width: 0;
            cursor: pointer;
            border-left-color: black;
            border-top-color: black;
        }

        .youtube_subs_button_white {
            width: 100%;
            height: 8.5vw;
            font-size: 3vw;
            font-weight: 600;
            color: black;
            background-color: white;
            border-radius: 10px;
            display: block;
            margin: auto;
            margin-top: 1vw;
            border-width: 1px;
            cursor: pointer;
            border-left-color: black;
            border-top-color: black;
        }



        .youtube_subs_button_div {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .youtube_subs_button_img {
            width: 8vw;
            height: 8vw;
        }
    }

@media screen and (max-width: 584px) {

    .journal_journal {
        line-height: 1;
        color: white;
        position: absolute;
        left: 16%;
        top: 24%;
        font-size: 3vw;
        text-shadow: black 1px 1px;
    }

    .journal_title {
        line-height: 1;
        color: white;
        position: absolute;
        left: 16%;
        top: 31%;
        font-size: 7vw;
        text-shadow: black 2px 2px;
    }
}

@media screen and (min-width: 585px) {

    .journal_journal {
        line-height: 1;
        color: white;
        position: absolute;
        left: 16%;
        top: 28%;
        font-size: 2.4vw;
        text-shadow: black 1px 1px;
    }

    .journal_title {
        line-height: 1;
        color: white;
        position: absolute;
        left: 16%;
        top: 36%;
        font-size: 4vw;
        text-shadow: black 2px 2px;
    }
}



    @media screen and (min-width: 585px) {

        .youtube_subs_button {
            width: 100%;
            height: 3.5vw;
            font-size: 1.2vw;
            font-weight: 600;
            color: black;
            background-color: white;
            border-radius: 10px;
            display: block;
            margin: auto;
            margin-top: 1vw;
            border-width: 0;
            cursor: pointer;
            border-left-color: black;
            border-top-color: black;
        }

        .youtube_subs_button_white {
            width: 100%;
            height: 3.5vw;
            font-size: 1.2vw;
            font-weight: 600;
            color: black;
            background-color: white;
            border-radius: 10px;
            display: block;
            margin: auto;
            margin-top: 1vw;
            border-width: 1px;
            cursor: pointer;
            border-left-color: black;
            border-top-color: black;
        }


        .youtube_subs_button_div {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .youtube_subs_button_img {
            width: 3vw;
            height: 3vw;
        }
    }


    @media screen and (max-width: 584px) {
        .content_padding {
            padding-left: 4%;
            padding-right: 4%;
        }
    }


    @media screen and (min-width: 585px) {
        .content_padding {
            padding-left: 2%;
            padding-right: 2%;
        }
    }
