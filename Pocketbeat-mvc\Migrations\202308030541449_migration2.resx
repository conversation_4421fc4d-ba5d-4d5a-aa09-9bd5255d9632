﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Target" xml:space="preserve">
    <value>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</value>
  </data>
  <data name="DefaultSchema" xml:space="preserve">
    <value>dbo</value>
  </data>
</root>