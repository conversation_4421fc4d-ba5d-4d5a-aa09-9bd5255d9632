﻿body {
    background-color: #252525;
}

.topnav-query {
    overflow: hidden;
    background-color: tomato;
    position: static;
    width: 100%;
}


.topnav {
    overflow: hidden;
    background-color: transparent;
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    height: 17vw;
}

    .topnav #myLinks {
        display: none;
        background-color: black;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        text-align: center;
        z-index: 2;
    }

.search-textbox {
    border: gray;
    border-style: solid;
    background: black;
    border-radius: 4px;
    margin-top: 20vw;
    padding: 2vw;
    font-size: 4vw;
    width: 80vw;
    height: 9vw;
    color: white;
    border-width: 2px;
}

    .search-textbox::placeholder {
        font-style: italic;
    }

.topnav a {
    color: white;
    padding: 14px 16px;
    text-decoration: none;
    font-size: 4vw;
    display: block;
    padding: 3vw;
}

    .topnav a.icon {
        background: transparent;
        display: block;
        position: absolute;
        right: 0;
        top: 0;
    }

.icon-unic {
    position: relative;
    top: -1vw;
    right: 2vw;
    font-size: 7vw;
    z-index: 3;
}

.logo {
    position: relative;
    top: -4vw;
    left: -1vw;
}

.logo-image {
    width: 36vw;
    height: 12vw;
    margin-top: 1vw;
    cursor: pointer;
}


.grid-2-cols {
    grid-template-columns: 1fr 1fr;
    grid-auto-rows: minmax(150px, auto);
    gap: 1vw;
    display: grid;
    max-width: 960px;
    margin: 0 auto;
    margin-left: 1vw;
    margin-right: 1vw;
}

.three-cols {
    grid-template-columns: 1fr 1fr 1fr;
    grid-auto-rows: minmax(150px, auto);
    gap: 1vw;
    display: grid;
    max-width: 960px;
    margin: 0 auto;
    margin-left: 1vw;
    margin-right: 1vw;
}

.col-1-2-ratio {
    grid-template-columns: 1fr 2fr;
    grid-auto-rows: minmax(150px, auto);
    gap: 1vw;
    display: grid;
    max-width: 960px;
    margin: 0 auto;
    margin-left: 1vw;
    margin-right: 1vw;
}

.col-2-1-ratio {
    grid-template-columns: 2fr 1fr;
    grid-auto-rows: minmax(150px, auto);
    gap: 1vw;
    display: grid;
    max-width: 960px;
    margin: 0 auto;
    margin-left: 1vw;
    margin-right: 1vw;
}


.col-2-1-1-ratio {
    grid-template-columns: 2fr 1fr 1fr;
    grid-auto-rows: minmax(150px, auto);
    gap: 1vw;
    display: grid;
    max-width: 960px;
    margin: 0 auto;
}

.four-cols {
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-auto-rows: minmax(150px, auto);
    gap: 1vw;
    display: grid;
    margin: 0 auto;
}

.five-cols {
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
    grid-auto-rows: minmax(150px, auto);
    gap: 1vw;
    display: grid;
    margin: 0 auto;
}

.six-cols {
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr;
    grid-auto-rows: minmax(150px, auto);
    gap: 1vw;
    display: grid;
    margin: 0 auto;
}

.double-width {
    grid-column: span 2; /* This spans 2 columns */
}

.col-1-2-1-ratio {
    grid-template-columns: 1fr 2fr 1fr;
    grid-auto-rows: minmax(150px, auto);
    gap: 1vw;
    display: grid;
    max-width: 960px;
    margin: 0 auto;
}


.topmost {
    cursor: pointer;
}


.col {
    cursor: pointer;
}

.col2 {
    cursor: pointer;
}

.col3 {
    cursor: pointer;
}

.col4 {
    cursor: pointer;
}

.col5 {
    cursor: pointer;
}

.col6 {
    cursor: pointer;
}

.cell {
    cursor: pointer;
}


img {
    width: 100%;
}

.genres {
    font-size: 2vw;
    padding-bottom: 2vw;
    color: #ea4c77;
}

.placeholder {
    height: 5vw;
}


.email-box {
    background-color: darkcyan;
    height: 54vw;
    margin-bottom: 8vw;
    box-shadow: 0px 9px 13px 3px black;
}










#email-prompt {
    position: fixed;
    top: 55vw;
    left: 1vw;
    right: 2vw;
    background-color: black;
    z-index: 10000;
    border-radius: 5px;
    padding: 10px;
    display: none;
    height: 52vw;
}


#prompt .prompt-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ccc;
    padding: 5px;
}

    #prompt .prompt-header .prompt-title {
        font-size: 1.2rem;
    }

    #prompt .prompt-header .prompt-close {
        cursor: pointer;
        font-size: 1.5rem;
    }

#prompt .prompt-body {
    padding: 10px;
}

#prompt input,
#prompt button {
    margin: 5px;
    padding: 5px;
}

#prompt button {
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

    #prompt button:hover {
        background-color: #3e8e41;
    }


#div_new {
    margin: auto;
    width: 75%;
    text-align: center;
    display: block;
    height: 15vw;
}

.country {
    height: 7vw;
    margin-left: 0;
    display: block;
    color: white;
    background-color: black;
    margin-top: 12vw;
    font-size: 3vw;
}


#input_country {
    display: none;
    width: 100%;
    height: 219px;
    margin: auto;
    border-radius: 4px;
    border-style: none;
    background-color: #222;
    margin-bottom: 5px;
    position: relative;
}

#under_country {
    font-size: 3vw;
    margin: 3px;
    color: white;
    text-align: left;
    margin-left: 7px;
    margin: auto;
    margin-top: 10px;
    margin-bottom: 20px;
}

#input_div2 {
    text-align: center;
    margin: auto;
    height: 4vw;
    width: 100%;
    height: 9vw;
}

#cross_button {
    position: absolute;
    background-color: #222;
    color: white;
    top: 0;
    right: 0;
}

    #cross_button:active {
        background-color: white;
    }


.prompt_email_txt {
    width: 70%;
    float: left;
    display: inline;
    margin: auto;
    height: 79%;
    font-size: 3vw;
    font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
    border: solid 1px black;
    text-align: left;
    /* margin-right: 2px; */
    padding-left: 10px;
    border-radius: 11px;
}

.prompt_email_button {
    margin: auto;
    background-color: #222;
    border-radius: 4px;
    border-style: solid;
    box-sizing: border-box;
    /* border-color: white; */
    color: #fff;
    cursor: pointer;
    display: inline-block;
    font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
    font-size: 3vw;
    font-weight: 700;
    line-height: 1.5;
    margin: 0;
    max-width: none;
    outline: none;
    overflow: hidden;
    position: relative;
    text-align: center;
    text-transform: none;
    user-select: none;
    -webkit-user-select: none;
    touch-action: manipulation;
    height: 80%;
    margin-top: -0.1vw;
    flex-grow: 1;
    /* width: 25%;*/
}

footer {
    background-color: black;
    margin-top: 0vw;
}

.footer-logo {
    margin: auto;
    width: 10%;
    margin-top: 10vw;
}

    .footer-logo img {
        width: 100%;
    }

footer a {
    color: white;
    padding: 14px 16px;
    text-decoration: none;
    font-size: 4vw;
    display: block;
    padding: 3vw;
    text-align: center;
}


#youtube-overlay {
    position: fixed;
    display: none;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgb(4 0 0 / 75%);
    z-index: 2;
    cursor: pointer;
}

#youtube-video {
    position: absolute;
    top: 52%;
    left: 50%;
    font-size: 50px;
    color: white;
    transform: translate(-50%,-50%);
    -ms-transform: translate(-50%,-50%);
    width: 67vw;
    height: 36vw;
}




#close-youtube-overlay {
    position: absolute;
    top: 2%;
    right: 2%;
    color: white;
    font-size: 50px;
    /* transform: translate(-50%,-50%); */
    /* -ms-transform: translate(-50%,-50%); */
    z-index:3;
}


#loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 2s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}


@media only screen and (max-width: 480px) {

    #youtube-video {
        position: absolute;
        top: 52%;
        left: 50%;
        font-size: 50px;
        color: white;
        transform: translate(-50%,-50%);
        -ms-transform: translate(-50%,-50%);
        width: 100vw;
        height: 60vw;
    }


    .genres {
        font-size: 3vw;
        padding-bottom: 5vw;
        color: #ea4c77;
        margin-right: 20%;
    }


    .email-box {
        background-color: darkcyan;
        height: 54vw;
        margin-bottom: 8vw;
        box-shadow: 0px 9px 13px 3px black;
    }


    .email-box-desc {
        text-align: center;
        position: relative;
        top: 4vw;
        font-size: 5vw;
        color: white;
        /*text-shadow: 0.3vw 0.3vw black;*/
    }

    .email_txt {
        width: 75%;
        display: block;
        margin: auto;
        height: 23%;
        font-size: 4vw;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        border: solid 1px white;
        text-align: left;
        padding-left: 10px;
        font-style: italic;
        position: relative;
        top: 11%;
    }

    .email_button {
        margin: auto;
        background-color: black;
        border-style: solid;
        box-sizing: border-box;
        border-color: black;
        color: #fff;
        cursor: pointer;
        display: block;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        font-size: 3.5vw;
        font-weight: 700;
        line-height: 1.5;
        text-align: center;
        user-select: none;
        -webkit-user-select: none;
        height: 17%;
        width: 24%;
        flex-grow: 1;
        position: relative;
        top: 17%;
        left: 25.5%;
    }

    .sub-to-youtube {
        height: 50vw;
        background-color: darkgray;
        position: relative;
        margin-bottom: 8vw;
        box-shadow: 0px 9px 13px 3px black;
    }

    .subscribe_desc {
        position: relative;
        top: 4vw;
        margin: auto;
        font-size: 5vw;
        text-align: center;
        color: white;
        text-shadow: 0.3vw 0.3vw black;
    }

    .scrollable-image-menu {
        position: relative;
        bottom: -6vw;
        height: 28vw;
        display: flex;
        overflow-x: auto;
    }

        .scrollable-image-menu a {
            text-decoration: none;
        }

    .icon_and_name {
        display: inline;
    }

    .icon_container {
        width: 20vw;
        height: 20vw;
    }

    .yt-icon {
        width: 78%;
        height: 78%;
        position: relative;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        border-radius: 50%;
    }

    .channel_name {
        text-align: center;
        margin-top: -1vw;
        text-decoration: none;
        color: white;
        font-size: 3vw;
    }
}


@media only screen and (max-width: 700px) and (min-width: 480px) {

    #youtube-video {
        position: absolute;
        top: 52%;
        left: 50%;
        font-size: 50px;
        color: white;
        transform: translate(-50%,-50%);
        -ms-transform: translate(-50%,-50%);
        width: 100vw;
        height: 60vw;
    }


    .genres {
        font-size: 1.5vw;
        padding-bottom: 3vw;
        color: #ea4c77;
    }


    .email-box {
        background-color: darkcyan;
        height: 32vw;
        margin-bottom: 8vw;
        box-shadow: 0px 9px 13px 3px black;
    }


    .email-box-desc {
        text-align: center;
        position: relative;
        top: 5vw;
        font-size: 3vw;
        color: white;
        text-shadow: 0.3vw 0.3vw black;
    }

    .email_txt {
        width: 80%;
        display: block;
        margin: auto;
        height: 18%;
        font-size: 3vw;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        border: solid 1px white;
        text-align: left;
        padding-left: 10px;
        font-style: italic;
        position: relative;
        top: 20%;
    }

    .email_button {
        margin: auto;
        background-color: black;
        border-style: solid;
        box-sizing: border-box;
        border-color: black;
        color: #fff;
        cursor: pointer;
        display: block;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        font-size: 3vw;
        font-weight: 700;
        line-height: 1.5;
        text-align: center;
        user-select: none;
        -webkit-user-select: none;
        height: 16%;
        width: 24%;
        flex-grow: 1;
        position: relative;
        top: 28%;
        left: 28%;
    }

    .sub-to-youtube {
        height: 33vw;
        background-color: darkgray;
        position: relative;
        margin-bottom: 8vw;
        box-shadow: 0px 9px 13px 3px black;
        width: 64.6vw;
        /* margin-top: -2vw;*/
    }

    .subscribe_desc {
        position: relative;
        top: 4vw;
        margin: auto;
        font-size: 3vw;
        text-align: center;
        color: white;
        text-shadow: 0.3vw 0.3vw black;
    }

    .scrollable-image-menu {
        position: relative;
        bottom: -5vw;
        height: 18vw;
        display: flex;
        overflow-x: auto;
    }

        .scrollable-image-menu a {
            text-decoration: none;
        }


    .icon_and_name {
        display: inline;
    }

    .icon_container {
        width: 14vw;
        height: 14vw;
    }

    .yt-icon {
        width: 78%;
        height: 78%;
        position: relative;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        border-radius: 50%;
    }

    .channel_name {
        text-align: center;
        margin-top: -1vw;
        text-decoration: none;
        color: white;
        font-size: 2.5vw;
    }
}

/*@media only screen and (max-width: 720px) and (min-width:600px) {

    #email-prompt {
        position: fixed;
        top: 18vw;
        left: 1vw;
        right: 2vw;
        background-color: black;
        z-index: 10000;
        border-radius: 5px;
        padding: 10px;
        display: none;
        height: 456px;
    }

    #div_new {
        margin: auto;
        width: 75%;
        text-align: center;
        display: block;
        height: 29vw;
        margin-top: -10vw;
    }

    .country {
        height: 7vw;
        margin-left: 0;
        display: block;
        color: white;
        background-color: black;
        margin-top: 12vw;
        font-size: 2vw;
        width: 100%;
    }

    #under_country {
        font-size: 2vw;
        margin: 3px;
        color: white;
        text-align: left;
        margin-left: 7px;
        margin: auto;
        margin-top: 10px;
        margin-bottom: 20px;
    }

    #input_div2 {
        text-align: center;
        margin: auto;
        height: 4vw;
        width: 100%;
        height: 6vw;
    }

    .prompt_email_txt {
        width: 70%;
        float: left;
        display: inline;
        margin: auto;
        height: 68%;
        font-size: 2vw;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        border: solid 1px black;
        text-align: left;*/
        /* margin-right: 2px; */
        /*padding-left: 10px;
        border-radius: 11px;
    }

    .prompt_email_button {
        margin: auto;
        background-color: #222;
        border-radius: 4px;
        border-style: solid;
        box-sizing: border-box;*/
        /* border-color: white; */
        /*color: #fff;
        cursor: pointer;
        display: inline-block;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        font-size: 2vw;
        font-weight: 700;
        line-height: 1.5;
        margin: 0;
        max-width: none;
        outline: none;
        overflow: hidden;
        position: relative;
        text-align: center;
        text-transform: none;
        user-select: none;
        -webkit-user-select: none;
        touch-action: manipulation;
        height: 68%;
        margin-top: -0.1vw;
        flex-grow: 1;*/
        /* width: 25%; */
        /*margin-left: -9vw;
        width: 11vw;
    }

    #youtube-video {
        position: absolute;
        top: 52%;
        left: 50%;
        font-size: 50px;
        color: white;
        transform: translate(-50%,-50%);
        -ms-transform: translate(-50%,-50%);
        width: 80vw;
        height: 48vw;
    }*/

    /* end of email prompt related styles*/

    /*.genres {
        font-size: 1.5vw;
        padding-bottom: 2vw;
        color: #ea4c77;
        margin-right: 20%;
    }




    .email-box {
        background-color: darkcyan;
        height: 24vw;
        margin-bottom: 8vw;
        box-shadow: 0px 9px 13px 3px black;
        margin-left: 1vw;
        margin-right: 1vw;
    }


    .email-box-desc {
        text-align: center;
        position: relative;
        top: 3vw;
        font-size: 2vw;
        color: white;
        text-shadow: 0.3vw 0.3vw black;
    }

    .email_txt {
        width: 80%;
        display: block;
        margin: auto;
        height: 18%;
        font-size: 2vw;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        border: solid 1px white;
        text-align: left;
        padding-left: 10px;
        font-style: italic;
        position: relative;
        top: 19%;
    }

    .email_button {
        margin: auto;
        background-color: black;
        border-style: solid;
        box-sizing: border-box;
        border-color: black;
        color: #fff;
        cursor: pointer;
        display: block;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        font-size: 2vw;
        font-weight: 700;
        line-height: 1.5;
        text-align: center;
        user-select: none;
        -webkit-user-select: none;
        height: 20%;
        width: 24%;
        flex-grow: 1;
        position: relative;
        top: 31%;
        left: 28%;
    }

    .sub-to-youtube {
        height: 25vw;
        background-color: darkgray;
        position: relative;
        margin-bottom: 8vw;
        box-shadow: 0px 9px 13px 3px black;
        width: 49.6vw;
    }

    .subscribe_desc {
        position: relative;
        top: 2vw;
        margin: auto;
        font-size: 2vw;
        text-align: center;
        color: white;
        text-shadow: 0.3vw 0.3vw black;
    }

    .scrollable-image-menu {
        position: relative;
        bottom: -3vw;
        height: 15vw;
        display: flex;
        overflow-x: auto;
    }

        .scrollable-image-menu a {
            text-decoration: none;
        }


    .icon_and_name {
        display: inline;
    }

    .icon_container {
        width: 11vw;
        height: 11vw;
    }

    .yt-icon {
        width: 78%;
        height: 78%;
        position: relative;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        border-radius: 50%;
    }

    .channel_name {
        text-align: center;
        margin-top: -1vw;
        text-decoration: none;
        color: white;
        font-size: 2vw;
    }
}*/

@media only screen and (max-width: 840px) and (min-width:700px) {

    #email-prompt {
        position: fixed;
        top: 18vw;
        left: 1vw;
        right: 2vw;
        background-color: black;
        z-index: 10000;
        border-radius: 5px;
        padding: 10px;
        display: none;
        height: 456px;
    }

    #div_new {
        margin: auto;
        width: 75%;
        text-align: center;
        display: block;
        height: 29vw;
        margin-top: -10vw;
    }

    .country {
        height: 7vw;
        margin-left: 0;
        display: block;
        color: white;
        background-color: black;
        margin-top: 12vw;
        font-size: 2vw;
        width: 100%;
    }

    #under_country {
        font-size: 2vw;
        margin: 3px;
        color: white;
        text-align: left;
        margin-left: 7px;
        margin: auto;
        margin-top: 10px;
        margin-bottom: 20px;
    }

    #input_div2 {
        text-align: center;
        margin: auto;
        height: 4vw;
        width: 100%;
        height: 6vw;
    }

    .prompt_email_txt {
        width: 70%;
        float: left;
        display: inline;
        margin: auto;
        height: 68%;
        font-size: 2vw;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        border: solid 1px black;
        text-align: left;
        /* margin-right: 2px; */
        padding-left: 10px;
        border-radius: 11px;
    }

    .prompt_email_button {
        margin: auto;
        background-color: #222;
        border-radius: 4px;
        border-style: solid;
        box-sizing: border-box;
        /* border-color: white; */
        color: #fff;
        cursor: pointer;
        display: inline-block;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        font-size: 2vw;
        font-weight: 700;
        line-height: 1.5;
        margin: 0;
        max-width: none;
        outline: none;
        overflow: hidden;
        position: relative;
        text-align: center;
        text-transform: none;
        user-select: none;
        -webkit-user-select: none;
        touch-action: manipulation;
        height: 68%;
        margin-top: -0.1vw;
        flex-grow: 1;
        /* width: 25%; */
        margin-left: -9vw;
        width: 11vw;
    }


    /* end of email prompt related styles*/


    #youtube-video {
        position: absolute;
        top: 52%;
        left: 50%;
        font-size: 50px;
        color: white;
        transform: translate(-50%,-50%);
        -ms-transform: translate(-50%,-50%);
        width: 75vw;
        height: 45vw;
    }

    .footer-logo {
        margin: auto;
        width: 5%;
        margin-top: 3vw;
    }

    footer a {
        color: white;
        padding: 14px 16px;
        text-decoration: none;
        font-size: 1.5vw;
        display: block;
        padding: 1.5vw;
        text-align: center;
    }


    .topnav a {
        color: white;
        /*padding: 14px 16px;*/
        text-decoration: none;
        font-size: 1.5vw;
        display: block;
        /*padding: 1vw;*/
    }

    #myLinks a {
        color: white;
        /*padding: 14px 16px;*/
        text-decoration: none;
        font-size: 1.5vw;
        display: block;
        /*padding: 1vw;*/
        padding: 1.5vw;
    }


    .search-textbox {
        border: gray;
        border-style: solid;
        background: black;
        border-radius: 4px;
        margin-top: 6vw;
        padding: 2vw;
        font-size: 1.5vw;
        width: 30vw;
        height: 4vw;
        color: white;
        border-width: 2px;
        margin-bottom: 1vw;
    }


    .icon-unic {
        position: relative;
        top: -2vw;
        right: 0vw;
        font-size: 3vw;
        z-index: 3;
    }

    .logo-image {
        width: 21vw;
        height: 7vw;
        margin-top: 1vw;
        cursor: pointer;
    }

    .topnav {
        overflow: hidden;
        background-color: transparent;
        position: absolute;
        width: 100%;
        top: 0;
        left: 0;
        height: 7.5vw;
    }

    .genres {
        font-size: 1.5vw;
        padding-bottom: 2vw;
        color: #ea4c77;
    }


    .email-box {
        background-color: darkcyan;
        height: 18vw;
        margin-bottom: 8vw;
        box-shadow: 0px 9px 13px 3px black;
    }


    .email-box-desc {
        text-align: center;
        position: relative;
        top: 11%;
        font-size: 2vw;
        color: white;
        text-shadow: 0.3vw 0.3vw black;
    }

    .email_txt {
        width: 80%;
        display: block;
        margin: auto;
        height: 18%;
        font-size: 2vw;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        border: solid 1px white;
        text-align: left;
        padding-left: 10px;
        font-style: italic;
        position: relative;
        top: 17%;
    }

    .email_button {
        margin: auto;
        background-color: black;
        border-style: solid;
        box-sizing: border-box;
        border-color: black;
        color: #fff;
        cursor: pointer;
        display: block;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        font-size: 2vw;
        font-weight: 700;
        line-height: 1.5;
        text-align: center;
        user-select: none;
        -webkit-user-select: none;
        height: 17%;
        width: 24%;
        flex-grow: 1;
        position: relative;
        top: 23%;
        left: 28%;
    }

    .sub-to-youtube {
        height: 6%;
        background-color: darkgray;
        position: relative;
        margin-bottom: 8vw;
        box-shadow: 0px 9px 13px 3px black;
        width: 49.6vw;
    }

    .subscribe_desc {
        position: relative;
        top: 6%;
        margin: auto;
        font-size: 2vw;
        text-align: center;
        color: white;
        text-shadow: 0.3vw 0.3vw black;
    }

    .scrollable-image-menu {
        position: relative;
        bottom: -1vw;
        height: 13vw;
        display: flex;
        overflow-x: auto;
    }

        .scrollable-image-menu a {
            text-decoration: none;
        }

    .icon_and_name {
        display: inline;
    }

    .icon_container {
        width: 9vw;
        height: 9vw;
    }

    .yt-icon {
        width: 78%;
        height: 78%;
        position: relative;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        border-radius: 50%;
    }

    .channel_name {
        text-align: center;
        margin-top: -1vw;
        text-decoration: none;
        color: white;
        font-size: 1.5vw;
    }
}



@media only screen and (min-width: 840px) {

    #email-prompt {
        position: fixed;
        top: 18vw;
        left: 1vw;
        right: 2vw;
        background-color: black;
        z-index: 10000;
        border-radius: 5px;
        padding: 10px;
        display: none;
        height: 456px;
    }

    #div_new {
        margin: auto;
        width: 75%;
        text-align: center;
        display: block;
        height: 29vw;
        margin-top: -10vw;
    }

    .country {
        height: 7vw;
        margin-left: 0;
        display: block;
        color: white;
        background-color: black;
        margin-top: 12vw;
        font-size: 2vw;
        width: 100%;
    }

    #under_country {
        font-size: 2vw;
        margin: 3px;
        color: white;
        text-align: left;
        margin-left: 7px;
        margin: auto;
        margin-top: 10px;
        margin-bottom: 20px;
    }

    #input_div2 {
        text-align: center;
        margin: auto;
        height: 4vw;
        width: 100%;
        height: 6vw;
    }

    .prompt_email_txt {
        width: 70%;
        float: left;
        display: inline;
        margin: auto;
        height: 68%;
        font-size: 2vw;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        border: solid 1px black;
        text-align: left;
        /* margin-right: 2px; */
        padding-left: 10px;
        border-radius: 11px;
    }

    .prompt_email_button {
        margin: auto;
        background-color: #222;
        border-radius: 4px;
        border-style: solid;
        box-sizing: border-box;
        /* border-color: white; */
        color: #fff;
        cursor: pointer;
        display: inline-block;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        font-size: 2vw;
        font-weight: 700;
        line-height: 1.5;
        margin: 0;
        max-width: none;
        outline: none;
        overflow: hidden;
        position: relative;
        text-align: center;
        text-transform: none;
        user-select: none;
        -webkit-user-select: none;
        touch-action: manipulation;
        height: 68%;
        margin-top: -0.1vw;
        flex-grow: 1;
        /* width: 25%; */
        margin-left: -9vw;
        width: 11vw;
    }


   /* end of email prompt related styles*/


    #youtube-video {
        position: absolute;
        top: 52%;
        left: 50%;
        font-size: 50px;
        color: white;
        transform: translate(-50%,-50%);
        -ms-transform: translate(-50%,-50%);
        width: 75vw;
        height: 45vw;
    }


    .footer-logo {
        margin: auto;
        width: 5%;
        margin-top: 3vw;
    }

    footer a {
        color: white;
        padding: 14px 16px;
        text-decoration: none;
        font-size: 1vw;
        display: block;
        padding: 1vw;
        text-align: center;
    }


    .topnav a {
        color: white;
        /*padding: 14px 16px;*/
        text-decoration: none;
        font-size: 1.5vw;
        display: block;
        /*padding: 1vw;*/
    }

    #myLinks a {
        color: white;
        /* padding: 14px 16px; */
        text-decoration: none;
        font-size: 1vw;
        display: block;
        /* padding: 1vw; */
        padding: 1vw;
    }


    .search-textbox {
        border: gray;
        border-style: solid;
        background: black;
        border-radius: 4px;
        margin-top: 6vw;
        padding: 1vw;
        font-size: 1vw;
        width: 30vw;
        height: 3vw;
        color: white;
        border-width: 2px;
        margin-bottom: 1vw;
    }


    .icon-unic {
        position: relative;
        top: -2vw;
        right: 0vw;
        font-size: 3vw;
        z-index:3;
    }

    .logo-image {
        width: 21vw;
        height: 7vw;
        margin-top: 1vw;
        cursor: pointer;
    }

    .topnav {
        overflow: hidden;
        background-color: transparent;
        position: absolute;
        width: 100%;
        top: 0;
        left: 0;
        height: 7.5vw;
    }

    .genres {
        font-size: 1vw;
        padding-bottom: 2vw;
        color: #ea4c77;
        margin-right: 20%;
    }

    .email-box {
        background-color: darkcyan;
        height: 17vw;
        margin-bottom: 8vw;
        box-shadow: 0px 9px 13px 3px black;
    }


    .email-box-desc {
        text-align: center;
        position: relative;
        top: 1vw;
        font-size: 1.5vw;
        color: white;
        text-shadow: 0.1vw 0.1vw black;
    }

    .email_txt {
        width: 80%;
        display: block;
        margin: auto;
        height: 18%;
        font-size: 2vw;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        border: solid 1px white;
        text-align: left;
        padding-left: 10px;
        font-style: italic;
        position: relative;
        top: 16%;
    }

    .email_button {
        margin: auto;
        background-color: black;
        border-style: solid;
        box-sizing: border-box;
        border-color: black;
        color: #fff;
        cursor: pointer;
        display: block;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        font-size: 1.5vw;
        font-weight: 700;
        line-height: 1.5;
        text-align: center;
        user-select: none;
        -webkit-user-select: none;
        height: 20%;
        width: 24%;
        flex-grow: 1;
        position: relative;
        top: 27%;
        left: 28%;
    }

    .sub-to-youtube {
        height: 17vw;
        background-color: darkgray;
        position: relative;
        margin-bottom: 8vw;
        box-shadow: 0px 9px 13px 3px black;
        width: 32.6vw;
    }

    .subscribe_desc {
        position: relative;
        top: 1vw;
        margin: auto;
        font-size: 1.5vw;
        text-align: center;
        color: white;
        text-shadow: 0.1vw 0.1vw black;
    }

    .scrollable-image-menu {
        position: relative;
        bottom: -1vw;
        height: 13vw;
        display: flex;
        overflow-x: auto;
    }

        .scrollable-image-menu a {
            text-decoration: none;
        }

    .icon_and_name {
        display: inline;
    }

    .icon_container {
        width: 9vw;
        height: 9vw;
    }

    .yt-icon {
        width: 78%;
        height: 78%;
        position: relative;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        border-radius: 50%;
    }

    .channel_name {
        text-align: center;
        margin-top: -1vw;
        text-decoration: none;
        color: white;
        font-size: 1.5vw;
    }
}





