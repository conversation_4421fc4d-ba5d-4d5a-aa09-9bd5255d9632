﻿namespace Pocketbeat_mvc
{
    public class CyclicCollection
    {
        private Dictionary<string, CyclicPropertyIterator<string>> propertyIterators;

        public CyclicCollection(Dictionary<string, List<string>> properties)
        {
            propertyIterators = new Dictionary<string, CyclicPropertyIterator<string>>();

            foreach (var property in properties)
            {
                propertyIterators[property.Key] = new CyclicPropertyIterator<string>(property.Value);
            }
        }

        public string GetCurrent(string propertyName)
        {
            if (propertyIterators.TryGetValue(propertyName, out var iterator))
            {
                return iterator.GetCurrent();
            }

            return null;
        }
    }
}
