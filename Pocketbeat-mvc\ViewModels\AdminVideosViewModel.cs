﻿using Pocketbeat_mvc.Models;

namespace Pocketbeat_mvc.ViewModels
{
    public class AdminVideosViewModel
    {
        public List<Artist> AllArtists { get; set; } = new List<Artist>();

        //public List<Genre> AllGenres { get; set; } = new List<Genre>();
        public Video Video { get; set; }
       
        public bool ViewForUpdate { get; set; }
        public bool AddSectionVisible { get; set; }
    }
}
