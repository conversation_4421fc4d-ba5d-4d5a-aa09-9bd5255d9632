﻿@model Pocketbeat_mvc.ViewModels.AdminArtistsViewModel
@using System.IO

@{
    ViewData["Title"] = "Index";
}

@{
    Layout = "~/Views/Shared/_adminPagesLayout.cshtml";
}


@section Styles{

    <link rel="stylesheet" href="~/css/AdminVideos_v2.css" />

}




<div class="container">
    <div style="/*margin-left: 50px; */ margin-bottom: 20px">
        <label id="lbl_entity">Add Artist</label>

    </div>

    <div id="add_entity">



        <form method="post" id="form" enctype="multipart/form-data">


            <div class="infobox">
                <label>Artist Name</label>
                @Html.TextBoxFor(m => m.Name)
            </div>



            <div class="button_container">
                <input type="submit" formaction="@Url.Action("Save","AdminArtists")" value="Add" class="btn1" />
            </div>

        </form>


    </div>


</div>


@section Scripts{


}


