﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Target" xml:space="preserve">
    <value>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</value>
  </data>
  <data name="DefaultSchema" xml:space="preserve">
    <value>dbo</value>
  </data>
</root>