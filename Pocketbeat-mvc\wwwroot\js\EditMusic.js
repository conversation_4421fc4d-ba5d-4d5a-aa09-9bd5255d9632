﻿function open_preview(IsMusic, id) { //music is a bool value
    var newTab = null;



    if (IsMusic == false) {
       
        

        var _url = "/Track/PreviewCollectionsTrack";


        var form = $("#form_track");
        var formData = new FormData(form[0]);

        $.ajax({
            type: "POST",
            url: _url,
            data: formData,
            async: false,
            contentType: false,
            processData: false,
            success: function (result) {

                console.log(result);

                newTab = window.open();
                newTab.document.write(result);
            }
        });

    }
    else {

        var _url = "/Music/PreviewEdit?music_id=" + id;

        var form = $("#form_music");
        var formData = new FormData(form[0]);

        $.ajax({
            type: "POST",
            url: _url,
            data: formData,
            async: false,
            contentType: false,
            processData: false,
            success: function (result) {

                console.log(result);

                newTab = window.open();
                newTab.document.write(result);
            }
        });
    }

}