﻿namespace Pocketbeat_mvc.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class migration1 : DbMigration
    {
        public override void Up()
        {
            DropForeignKey("dbo.Artists", "Video_Id", "dbo.Videos");
            DropIndex("dbo.Artists", new[] { "Video_Id" });
            CreateTable(
                "dbo.Admins",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Email = c.String(),
                        Password = c.String(),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.VideoArtists",
                c => new
                    {
                        Video_Id = c.Int(nullable: false),
                        Artist_Id = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Video_Id, t.Artist_Id })
                .ForeignKey("dbo.Videos", t => t.Video_Id, cascadeDelete: true)
                .ForeignKey("dbo.Artists", t => t.Artist_Id, cascadeDelete: true)
                .Index(t => t.Video_Id)
                .Index(t => t.Artist_Id);
            
            DropColumn("dbo.Artists", "Video_Id");
        }
        
        public override void Down()
        {
            AddColumn("dbo.Artists", "Video_Id", c => c.Int());
            DropForeignKey("dbo.VideoArtists", "Artist_Id", "dbo.Artists");
            DropForeignKey("dbo.VideoArtists", "Video_Id", "dbo.Videos");
            DropIndex("dbo.VideoArtists", new[] { "Artist_Id" });
            DropIndex("dbo.VideoArtists", new[] { "Video_Id" });
            DropTable("dbo.VideoArtists");
            DropTable("dbo.Admins");
            CreateIndex("dbo.Artists", "Video_Id");
            AddForeignKey("dbo.Artists", "Video_Id", "dbo.Videos", "Id");
        }
    }
}
