﻿using System;
using System.Collections.Generic;


var properties = new Dictionary<string, List<string>>
        {
            { "animals", new List<string> { "cat", "dog", "mouse" } },
            { "fruits", new List<string> { "mango", "banana", "apple" } },
            { "countries", new List<string> { "USA", "Italy", "France" } }
        };

var collection = new CyclicCollection(properties);

for (int i = 0; i < 10; i++)
{
    Console.WriteLine($"Animal: {collection.GetCurrent("animals")}");
    
}

int sdfd = 0;




class CyclicPropertyIterator<T>
{
    private List<T> items;
    private int currentIndex;

    public CyclicPropertyIterator(IEnumerable<T> inputItems)
    {
        items = new List<T>(inputItems);
        currentIndex = 0;
    }

    public T GetCurrent()
    {
        if (items.Count == 0)
            return default;

        T currentItem = items[currentIndex];
        currentIndex = (currentIndex + 1) % items.Count;
        return currentItem;
    }
}

class CyclicCollection
{
    private Dictionary<string, CyclicPropertyIterator<string>> propertyIterators;

    public CyclicCollection(Dictionary<string, List<string>> properties)
    {
        propertyIterators = new Dictionary<string, CyclicPropertyIterator<string>>();

        foreach (var property in properties)
        {
            propertyIterators[property.Key] = new CyclicPropertyIterator<string>(property.Value);
        }
    }

    public string GetCurrent(string propertyName)
    {
        if (propertyIterators.TryGetValue(propertyName, out var iterator))
        {
            return iterator.GetCurrent();
        }

        return null;
    }
}

