﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Target" xml:space="preserve">
    <value>H4sIAAAAAAAACuxa3W7bNhS+H7B3EHS1DamVpDdbYLfInGQzlj/EbrFdBbREO0QpShWpLNmwJ9vFHmmvsEP9UiRlS7ZjdEVRoHBEnu/86vAcHv379z/Dt08hdR5xwknERu7R4NB1MPOjgLDlyE3F4tX37ts3X381PA/CJ+d9ue+13AeUjI/cByHiE8/j/gMOER+ExE8iHi3EwI9CDwWRd3x4+IN3dORhgHABy3GGdykTJMTZH/DnOGI+jkWK6FUUYMqL57AyzVCdaxRiHiMfj9zbyP+AxRwjcR8++oOcwHVOKUEgzBTThesgxiKBBIh68o7jqUgitpzG8ADR2XOMYd8CUY4LFU7q7V21OTyW2ng1YQnlp1xEYU/Ao9eFeTydfCMj5zYuDHgOhhbPUuvMiCP3NAgJ4OqcTsY0kbtaDDzIyA6cevEVLB5UEQGBI/8dOOOUijTBI4ZTkSAKFOmcEv8X/DyLPmA2YimlqoAgIqw1HsCj2ySKcSKe7/CiEHsSuI7XpPN0wopMocl1mjDx+th1roE5mlNc+V/RfyqiBP+EGU6QwMEtEgIn4L5JgDMLGtw1XuchIrRkBxEH74/rXKGnS8yW4mHkwk/XuSBPOCifFCK8YwReNyASSYrXcblFnP8eJcGOGQ29OkxWB08iCBcbRE9G9yV8Wh2bG0j+3n0MXaNHsszcpDF9TwIcQe68wzRb5g8kzlPoIFu6zxZwkAsHGy+SKLyL5PtjW7+foWSJITpm0YpN0yhN/E3jL0PsH34Z2Zfoa42+36JUpHN8SdiHF09hmS9mRFD84qwgmyeYv7xG8Jv4iE5CtHx5pX6OEvJHxMS++E0/pijB++HVsOQ0RFAq7Nmc+2Gq2HQ/DMcJZEQcnMF/JS/5ewY1uKUq6np+6MfDrs6R8ohYeY6Uh02Xc+SU88gnmWxqFtLlaqp9zgKni5C55yplwXtQApMYil6QZOR+Z9hzDXB1itbAZdm1EnnoKWqapyj0WAIRKG8LC4D/keUkhX6pOEx5EVq68BJsioXaToBLa8OXMud9hreGvAwJk77QeQ1AWcMY9IUzNHLFRBqGEaTK1lXBrLcuHcOm0qNWoSlq90BRoCrh9VapqbfllanCo+69vbz5Lpt0r6VLH16hOIa0pXTtxRNnmrfs41fT/o1smGN4fsPEejBXnKB7g2yqrQJrkPSCJFzIcJ8jmd3GQWhsy16GlkArWTTi3XRVGX7ldvm7aNraG2odp7bdBagTQuuZaYZVF9vpsrsSRFFi6YHHEU1D1tZHr6IuuloVoHjUHaPuWVWY+qmJNPQ0I+jWVtJ8sVMLd9153Vzb+kb39m2euzZwbgvhy3hXbTpVFPX5J+OdIktu75z8YOjvmxa6l3FNoyNTYRoL3fHUrkuFU593RysbKxWpfNZDpmbf1BCrudQd0+iOVFRj0euM2+iCVMzGQnc8W6fTaoBivTu6valZYYreHMwOpsUovZEbrYoK2ljYf1pqVlCW3GQUkJ0SkUFlTTv2c0EWiLb3vKXmNE3WKXHlkLb0JU1WCbChbEURu6FsOUpP4fRq2HS3URTrW6pgq4pjrQgeFgXp+nmWUaHmW1wHZH8Ei0F1On3mAocDuWEw/UjHlEB41xuuECMLzEU+YHFhnnSszcM+ndmUx3lAuwyo9j4iItKka4dA6+5KVk2F2CNK/AeUfBOip29VqE0mPz3A+k53Pg/TmxOVlzCZpSj831rMMgXYKmTNm/6t4Jq3+dtJZrux3wqx5VZ+K0zLzfvutG7cO+9S9d0Bt92SbwVquQkP4Lf8GmXNC9MvJ2yeS1fUW+bm1gJofTap+Sg5xbgPnrAAP43cPzOiE2fy631Jd+DcJFB9nDiHzl8bZucNuFeEPdhvNxawls/63ez6OcCRUb3fsDNMscDOqZ+XY2PEfRSYtpOV7Eru5V25LkOnuQHAQTzhRJ4aiELJy+HDHXCHGXyE+SRGtKG3Wbh3OeukUhWevnKGYwx+hysYU8Uu7FY1LBW2ZuF1FmgdrnSMIVubo3jR4kC77z67KOrh1z2H0crecudxZB/SmeOLDtO49mFc3lrCiTeXCSvPl+UAr9OgbsWczordMo6yDvHaZ3g2aPvQzI7cKntjtZVLmxadZ4nV0K/bSLHlvsY8cozL6bUjxKYljPul7mPDXuqXg8qO6tuvhCzZ0hyc7M8APeam5lUQ5AnlA2jIVJwsawj5OTTDWTavQcs9E7aIylylSVRu0UqtKywQlLhIqrhAvoBlH3OefWjyHtE0u6aY42DCblIRpwJUxuGcNr54lQlvFf9sONyUeXgTZ9+C7EIFEJPIKv2G/ZgSGlRyX1hqvRYImUmLXln6UsieeQk6FkjXEesIVJivOgBmOIxl+PIbNkWPANJfNvjm4hIvkf9c3ui1g6x3RNPswzOClgkKeYFR08OfEMPwSf6b/wAAAP//AwDdhrFxBzAAAA==</value>
  </data>
  <data name="DefaultSchema" xml:space="preserve">
    <value>dbo</value>
  </data>
</root>