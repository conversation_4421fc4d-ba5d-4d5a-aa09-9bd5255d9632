﻿namespace Pocketbeat_mvc
{
    public class CyclicStringIterator
    {
        private List<string> strings;
        private int currentIndex;

        public CyclicStringIterator(IEnumerable<string> inputStrings)
        {
            strings = new List<string>(inputStrings);
            currentIndex = 0;
        }

        public string GetCurrent()
        {
            if (strings.Count == 0)
                return null;

            string currentString = strings[currentIndex];
            currentIndex = (currentIndex + 1) % strings.Count; // Move to the next index in a cyclic manner
            return currentString;
        }
    }
}
