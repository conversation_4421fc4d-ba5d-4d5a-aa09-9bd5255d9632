﻿@{
    Layout = "~/Views/Shared/_adminPagesLayout.cshtml";
}



@section Styles{


    <link rel="stylesheet" href="~/css/chosen.min.css" />

    <style>

        body {
            background-color: black;
        }

        .infobox {
            margin-top: 15px;
            margin-left: 50px;
            height: 500px;
            min-height: 60px;
            background-color: black;
            color: white;
            border: solid white;
            border-width: 1px;
            width: 200px;
        }

            .infobox label {
                width: 100%;
                height: 10px;
                font-size: x-small;
                font-size: x-small;
                display: block;
            }

            .infobox input {
                width: 90%;
                font-size: large;
                background-color: black;
                color: white;
                border: none;
            }

            .infobox .input_list {
                border: solid;
                border-color: #423a3a;
                margin-bottom: 2px;
            }

            .infobox textarea {
                font-size: large;
                background-color: black;
                color: white;
                border: none;
                width: 700px;
                height: 140px;
            }

        .btn1 {
            color: white;
            background-color: black;
            border-radius: 3px;
            border-color: white;
            padding-top: 11px;
            padding-bottom: 10px;
            padding-left: 20px;
            padding-right: 20px;
        }
    </style>



}



<div class="container">


    @try
    {

        @using (Data data = new Data())
        {
            <div>

                <div class="infobox" style="display:inline-block">
                    <label>email stats</label>
                    <table>
                        <tr><td>Total emails</td><td>@data.Emails.Count()</td></tr>
                        @{
                            var starttime = DateTimeOffset.Now.Date.AddDays(-30);
                        }
                        <tr><td>Last 30 days</td><td>@data.Emails.Where(e => e.SavedTime > starttime).Count()</td></tr>
                        @{
                            starttime = DateTimeOffset.Now.Date.AddDays(-7);
                        }
                        <tr><td>Last 7 days</td><td>@data.Emails.Where(e => e.SavedTime > starttime).Count()</td></tr>
                        @{
                            starttime = DateTimeOffset.Now.Date.AddHours(-48);
                        }
                        <tr><td>Last 48 hours</td><td>@data.Emails.Where(e => e.SavedTime > starttime).Count()</td></tr>
                        @{
                            starttime = DateTimeOffset.Now.Date.AddHours(-24);
                        }
                        <tr><td>Last 24 hours</td><td>@data.Emails.Where(e => e.SavedTime > starttime).Count()</td></tr>
                        @{
                            starttime = DateTimeOffset.Now.Date.AddHours(-12);
                        }
                        <tr><td>Last 12 hours</td><td>@data.Emails.Where(e => e.SavedTime > starttime).Count()</td></tr>
                        @{
                            starttime = DateTimeOffset.Now.Date.AddHours(-6);
                        }
                        <tr><td>Last 6 hours</td><td>@data.Emails.Where(e => e.SavedTime > starttime).Count()</td></tr>
                        @{
                            starttime = DateTimeOffset.Now.Date.AddMinutes(-60);
                        }
                        <tr><td>Last 1 hour</td><td>@data.Emails.Where(e => e.SavedTime > starttime).Count()</td></tr>
                    </table>

                </div>

                <div class="infobox" style="display:inline-block">
                    <label>country stats</label>
                    <table>
                        <tr><td>Unknown</td><td>@data.Emails.Where(e => e.Country == "NA" || e.Country == null || e.Country == "").Count()</td></tr>
                        <tr><td>Sweden</td><td>@data.Emails.Where(e => e.Country == "Sweden").Count()</td></tr>
                        <tr><td>France</td><td>@data.Emails.Where(e => e.Country == "France").Count()</td></tr>
                        <tr><td>Uniten Kingdom</td><td>@data.Emails.Where(e => e.Country == "United Kingdom").Count()</td></tr>
                        <tr><td>Gernmany</td><td>@data.Emails.Where(e => e.Country == "Germany").Count()</td></tr>
                        <tr><td>Spain</td><td>@data.Emails.Where(e => e.Country == "Spain").Count()</td></tr>
                        <tr><td>Poland</td><td>@data.Emails.Where(e => e.Country == "Poland").Count()</td></tr>
                        <tr><td>Italy</td><td>@data.Emails.Where(e => e.Country == "Italy").Count()</td></tr>
                        <tr><td>USA</td><td>@data.Emails.Where(e => e.Country == "United States").Count()</td></tr>
                        <tr><td>Denmark</td><td>@data.Emails.Where(e => e.Country == "Denmark").Count()</td></tr>
                        <tr><td>NOrway</td><td>@data.Emails.Where(e => e.Country == "Norway").Count()</td></tr>

                    </table>

                </div>

            </div>

            <div style="margin:50px">
                <div>
                    <button class="btn1" style="margin-left: 0px; width: 200px;" onclick="location.href='@Url.Action("DownloadFile", "AdminFans" , new { listType = "Full_email" })'">Download Full List</button>
                    <button class="btn1" style="margin-left: 50px; width: 200px;" onclick="location.href='@Url.Action("DownloadFile", "AdminFans" , new { listType = "Full_country" })'">Download Full List</button>
                </div>
                <div style="margin: 30px; margin-left: 0px; width: 200px;">
                    <button class="btn1" onclick="location.href='@Url.Action("DownloadFile", "AdminFans" , new { listType = "Add_ons" })'">Download Ad ons</button>
                    <label style="color: white; font-size: xx-small; display: block;">Last download @AppInfo.GetValue("last_downloaded_date")</label>
                </div>
            </div>

        }

    }
    catch (Exception ex)
    {

    }

</div>