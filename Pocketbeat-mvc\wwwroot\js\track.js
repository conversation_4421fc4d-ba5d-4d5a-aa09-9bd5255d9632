﻿var result = null;
var tempvar = null;


const promptOverlay = document.getElementById('email-prompt-overlay');
const prompt = document.getElementById('email-prompt');
const promptClose = document.getElementById('cross_button');

function trackDownloadButtonClick() {

    displayPrompt();

}

promptClose.addEventListener('click', function () {

    promptOverlay.style.display = 'none';
    prompt.style.display = 'none';
    document.body.style.overflow = 'auto';


});


function displayPrompt() {
    promptOverlay.style.display = 'block';
    prompt.style.display = 'block';
    document.body.style.overflow = 'hidden';

}

function submitEmail(_email,track_id,selected_country_index) {

    if (isValidEmail(_email)) {

        var countryToSave = "";

        if (selected_country_index == 0) {

            countryToSave = "NA";

        }
        else {

            countryToSave = document.getElementById("country").options[selected_country_index].text;
        }






        //write ajax call to this view's controllers `ManageTrackRequst` method
        //and get boolean as a response


        $.ajax({
            url: "/Track/ManageTrackRequst",
            type: "POST",
            data: { email: _email, track_id: track_id, country: countryToSave },
            success: function (response) {
                if (response) {
                    // do something if response is true
                } else {
                    // do something if response is false
                }
            },
            error: function (xhr) {
                // handle error
            }
        });


        promptOverlay.style.display = 'none';
        prompt.style.display = 'none';
        document.body.style.overflow = 'auto';


        messagePopup("Email Send", "green");



    }
    else {

        messagePopup("Wrong format", "red");
    }



}

function isValidEmail(email) {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
}


function messagePopup(message,color) {
    // Create the popup element
    var popup = document.createElement("div");
    popup.innerHTML = message;
    popup.style.position = "fixed";
    popup.style.left = "50%";
    popup.style.top = "50%";
    popup.style.transform = "translate(-50%, -50%)";
    popup.style.backgroundColor = "white";
    popup.style.border = "1px solid black";
    popup.style.padding = "10px";
    popup.style.color = color;
    popup.style.fontWeight = "900";

    // Add the popup to the page
    document.body.appendChild(popup);

    // Hide the popup after 1 second
    setTimeout(function () {
        popup.style.display = "none";
    }, 1000);
}