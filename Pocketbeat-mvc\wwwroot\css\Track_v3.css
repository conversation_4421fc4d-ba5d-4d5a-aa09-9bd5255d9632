﻿.container1 {
   
}

.main {
    text-align: center;
    width: 100%;
    margin: auto;
    position: relative;
}

=.back_button {
    width: 20px;
    height: 20px;
    position: absolute;
    top: 0;
    left: 0;
}

.back_button:hover {
    background-color: lightgray;
}

@media screen and (max-width: 584px) {

    .related_artists1 {
        /*height: 80vw;*/
        text-align: center;
        overflow-x: auto;
        /* overflow-y: hidden; */
        /* white-space: nowrap;*/
    }

        .related_artists1 .artist_image {
            display: inline;
            /* height: 100%; */
            width: 80%;
            margin-right: -0.3vw;
        }
}

@media screen and (min-width: 585px) {

    .related_artists1 {
        height: 24vw;
        text-align: left;
        overflow-x: auto;
        overflow-y: hidden;
        white-space: nowrap;
    }

        .related_artists1 .artist_image {
            display: inline;
            height: 100%;
        }
}


@media screen and (max-width: 584px) {
    .track_title_and_artist {
        display: block;
        text-align: left;
        font-size: 2.5vw;
        margin-bottom :0.5vw;
    }

}

@media screen and (min-width: 585px) {
    .track_title_and_artist {
        display: block;
        text-align: left;
        font-size: 1.2vw;
        margin-bottom: 0.5vw;
    }
}

@media screen and (max-width: 584px) {
    .placeholder_div {
        height: 6vw;
    }
}
@media screen and (min-width: 585px) {

    .placeholder_div {

    }
}



@media screen and (max-width: 584px) {
    .main .track_name {
        display: inline-block;
        min-width: 100px;
        color: black;
        margin: 0;
        font-size: 3vw;
    }
}

@media screen and (min-width: 585px) {
    .main .track_name {
        display: inline-block;
        min-width: 100px;
        color: black;
        margin: 0;
        font-size: 1.2vw;
    }
}



@media screen and (max-width: 584px) {

    .description {
        margin-bottom: 2vw;
        text-align: left;
        width: 100%;
        font-size: 3vw;
    }

}

@media screen and (min-width: 585px) {

    .description {
        margin-bottom: 2vw;
        text-align: left;
        width: 100%;
        font-size: 1.2vw;
    }
}




.related_styles {
    height: 32vw;
    text-align: left;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
}

    .related_styles .style_image {
        display: inline;
        height: 100%;
    }




.video-container {
    position: relative;
    padding-bottom: 56.25%;
    /* 16:9 */
        height: 0;
        margin-bottom: 1vw;
        margin-top: 1vw;
    }

    .video-container iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }

.sc_embedment_container {
    width: 100%;
    margin-bottom: 1vw;
}

@media screen and (max-width: 400px) {

    .desktopImage {
        display: none;
    }


    .mobileImage {
        display: block;
    }
}


@media screen and (min-width: 401px) {

    .desktopImage {
        display: block;
    }


    .mobileImage {
        display: none;
    }
}

@media screen and (max-width: 584px) {

    .section_title {
        text-align: left;
        margin-top: 2vw;
        margin-bottom: 2vw;
        font-weight: 900;
        font-size: 3vw;
        visibility:hidden;
    }
}

@media screen and (min-width: 585px) {

    .section_title {
        text-align: left;
        margin-top: 2vw;
        margin-bottom: 0.5vw;
        font-weight: 900;
        font-size: 1.2vw;
    }
}

.related_journals {
}

    .related_journals img {
        width: 100%;
    }

@media screen and (max-width: 584px) {
    .section {
        margin-bottom: 10vw;
        margin-top: 10vw;
    }
}

@media screen and (min-width: 585px) {
    .section {
        margin-bottom: 3vw;
    }
} 



::-webkit-scrollbar {
    height: 4px;
    /* height of horizontal scrollbar ← You're missing this */
    width: 4px;
    /* width of vertical scrollbar */
    border: 1px solid #d5d5d5;
}





.support_overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
}

.support_heading {
    color: white;
    text-align: center;
    font-size: 2vw;
}

.support_body {
    color: white;
    text-align: left;
}

#support_closeButton {
    position: absolute;
    top: 0;
    background-color: white;
    color: white;
    height: 3vw;
}

/*email prompt*/
@media screen and (max-width: 584px) {
    /* #email-prompt-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 9999;
            display: none;
        }*/

    #email-prompt {
        position: fixed;
        top: 55vw;
        left: 1vw;
        right: 2vw;
        background-color: black;
        z-index: 10000;
        border-radius: 5px;
        padding: 10px;
        display: none;
        height: 300px;
    }


    #prompt .prompt-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #ccc;
        padding: 5px;
    }

        #prompt .prompt-header .prompt-title {
            font-size: 1.2rem;
        }

        #prompt .prompt-header .prompt-close {
            cursor: pointer;
            font-size: 1.5rem;
        }

    #prompt .prompt-body {
        padding: 10px;
    }

    #prompt input,
    #prompt button {
        margin: 5px;
        padding: 5px;
    }

    #prompt button {
        background-color: #4CAF50;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
    }

        #prompt button:hover {
            background-color: #3e8e41;
        }


    #div_new {
        margin: auto;
        width: 75%;
        text-align: center;
        display: block;
        height: 15vw;
    }

    .country {
        height: 7vw;
        font-size: 3vw;
        margin-left: 0;
        display: block;
        margin-bottom: 20px;
    }


    #input_country {
        display: none;
        width: 100%;
        height: 219px;
        margin: auto;
        border-radius: 4px;
        border-style: none;
        background-color: #222;
        margin-bottom: 5px;
        position: relative;
    }

    #under_country {
        font-size: 3vw;
        margin: 3px;
        color: white;
        text-align: left;
        margin-left: 7px;
        margin: auto;
        margin-top: 10px;
        margin-bottom: 20px;
    }

    #input_div2 {
        text-align: center;
        margin: auto;
        height: 4vw;
        width: 100%;
        height: 50px;
    }

    #cross_button {
        position: absolute;
        background-color: #222;
        color: white;
        top: 0;
        right: 0;
    }

        #cross_button:active {
            background-color: white;
        }

    .prompt_email_txt {
        width: 70%;
        float: left;
        display: inline;
        margin: auto;
        height: 68%;
        font-size: 3vw;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        border: solid 1px black;
        text-align: left;
        margin-right: 2px;
        padding-left: 10px;
        border-radius: 11px;
    }

    .prompt_email_button {
        width: 22%;
        margin: auto;
        background-color: #222;
        border-radius: 4px;
        border-style: solid;
        box-sizing: border-box;
        border-color: white;
        color: #fff;
        cursor: pointer;
        display: inline-block;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        font-size: 3vw;
        font-weight: 700;
        /* line-height: 1.5; */
        margin: 0;
        max-width: none;
        outline: none;
        overflow: hidden;
        position: relative;
        text-align: center;
        text-transform: none;
        user-select: none;
        -webkit-user-select: none;
        touch-action: manipulation;
        height: 72%;
        /* margin-top: -0.1vw; */
        flex-grow: 1;
    }
}

@media screen and (min-width: 585px) {
    #email-prompt-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 9999;
        display: none;
    }

    #email-prompt {
        position: fixed;
        top: 20vw;
        left: 1vw;
        right: 2vw;
        background-color: black;
        z-index: 10000;
        border-radius: 5px;
        padding: 10px;
        display: none;
        height: 18vw;
    }


    #prompt .prompt-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #ccc;
        padding: 5px;
    }

        #prompt .prompt-header .prompt-title {
            font-size: 1.2rem;
        }

        #prompt .prompt-header .prompt-close {
            cursor: pointer;
            font-size: 1.5rem;
        }

    #prompt .prompt-body {
        padding: 10px;
    }

    #prompt input,
    #prompt button {
        margin: 5px;
        padding: 5px;
    }

    #prompt button {
        background-color: #4CAF50;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
    }

        #prompt button:hover {
            background-color: #3e8e41;
        }


    #div_new {
        margin: auto;
        width: 55vw;
        text-align: center;
        display: block;
        height: 15vw;
    }

    .country {
        height: 3vw;
        font-size: 1.5vw;
        margin-left: 0;
        display: block;
    }


    #input_country {
        display: none;
        width: 100%;
        height: 219px;
        margin: auto;
        border-radius: 4px;
        border-style: none;
        background-color: #222;
        margin-bottom: 5px;
        position: relative;
    }

    #under_country {
        font-size: 1.5vw;
        margin: 3px;
        color: white;
        text-align: left;
        margin-left: 7px;
        margin: auto;
        margin-top: 10px;
        margin-bottom: 1vw;
    }

    #input_div2 {
        text-align: center;
        margin: auto;
        height: 4vw;
        width: 100%;
    }


    #cross_button {
        position: absolute;
        background-color: #222;
        color: white;
        top: 0;
        right: 0;
    }

        #cross_button:active {
            background-color: white;
        }

    .prompt_email_txt {
        width: 75%;
        float: left;
        display: inline;
        margin: auto;
        height: 68%;
        font-size: 1.2vw;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        border: solid 1px black;
        text-align: left;
        margin-right: 2px;
        padding-left: 10px;
        border-radius: 11px;
    }

    .prompt_email_button {
        width: 22%;
        margin: auto;
        background-color: #222;
        border-radius: 4px;
        border-style: solid;
        box-sizing: border-box;
        border-color: white;
        color: #fff;
        cursor: pointer;
        display: inline-block;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        font-size: 1.5vw;
        font-weight: 700;
        /* line-height: 1.5; */
        margin: 0;
        max-width: none;
        outline: none;
        overflow: hidden;
        position: relative;
        text-align: center;
        text-transform: none;
        user-select: none;
        -webkit-user-select: none;
        touch-action: manipulation;
        height: 76%;
        /* margin-top: -0.1vw; */
        flex-grow: 1;
    }
}




@media screen and (min-width: 585px) {
    #email-prompt-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 9999;
        display: none;
    }

    #email-prompt {
        position: fixed;
        top: 20vw;
        left: 0vw;
        right: 0vw;
        background-color: black;
        z-index: 10000;
        border-radius: 5px;
        padding: 10px;
        display: none;
        height: 22vw;
    }


    #prompt .prompt-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #ccc;
        padding: 5px;
    }

        #prompt .prompt-header .prompt-title {
            font-size: 1.2rem;
        }

        #prompt .prompt-header .prompt-close {
            cursor: pointer;
            font-size: 1.5rem;
        }

    #prompt .prompt-body {
        padding: 10px;
    }

    #prompt input,
    #prompt button {
        margin: 5px;
        padding: 5px;
    }

    #prompt button {
        background-color: #4CAF50;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
    }

        #prompt button:hover {
            background-color: #3e8e41;
        }


    #div_new {
        margin: auto;
        margin-top: 3vw;
        width: 55vw;
        text-align: center;
        display: block;
        height: 20vw;
    }

    .country {
        height: 3vw;
        font-size: 1.vw;
        margin-left: 0;
        display: block;
        color: white;
        background-color: black;
    }


    #input_country {
        display: none;
        width: 100%;
        height: 219px;
        margin: auto;
        border-radius: 4px;
        border-style: none;
        background-color: #222;
        margin-bottom: 5px;
        position: relative;
    }

    #under_country {
        font-size: 1.5vw;
        margin: 3px;
        color: white;
        text-align: left;
        margin-left: 7px;
        margin: auto;
        margin-top: 10px;
        margin-bottom: 1vw;
    }

    #input_div2 {
        text-align: center;
        margin: auto;
        height: 4vw;
        width: 100%;
    }


    #cross_button {
        position: absolute;
        background-color: #222;
        color: white;
        top: 0;
        right: 0;
    }

        #cross_button:active {
            background-color: white;
        }

    .prompt_email_txt {
        width: 75%;
        float: left;
        display: inline;
        margin: auto;
        height: 68%;
        font-size: 1.2vw;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        border: solid 1px black;
        text-align: left;
        margin-right: 2px;
        padding-left: 10px;
        border-radius: 11px;
    }

    .prompt_email_button {
        width: 22%;
        margin: auto;
        background-color: #222;
        border-radius: 4px;
        border-style: solid;
        box-sizing: border-box;
        border-color: white;
        color: #fff;
        cursor: pointer;
        display: inline-block;
        font-family: "Lato", "Helvetica Neue", Arial, sans-serif;
        font-size: 1.5vw;
        font-weight: 700;
        /* line-height: 1.5; */
        margin: 0;
        max-width: none;
        outline: none;
        overflow: hidden;
        position: relative;
        text-align: center;
        text-transform: none;
        user-select: none;
        -webkit-user-select: none;
        touch-action: manipulation;
        height: 76%;
        /* margin-top: -0.1vw; */
        flex-grow: 1;
    }
}


@media screen and (max-width: 584px) {
    .parent {
        
        top: 0;
    }
}


@media screen and (max-width: 584px) {
    .profile_box_container {
        width: 100%;
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
    }

    .profile_box {
        width: 65vw;
        height: 100vw;
        display: inline-block;
        /*text-align: left;*/
    }

        .profile_box img {
            height: 83%;
            width: 90%;
            cursor: pointer;
            transition: all 0.5s ease;
        }

            .profile_box img:hover {
                transform: scale(1.1);
            }

        .profile_box .artist_name {
            width: 65vw;
            text-align: center;
            color: black;
            margin-bottom: 3px;
            display: block;
            font-size: 4vw;
            font-weight: 600;
        }

        .profile_box .state {
            border: solid;
            color: white;
            display: block;
        }
}

@media screen and (min-width: 585px) {
    .profile_box_container {
        width: 100%;
        display: flex;
        justify-content: start;
        flex-wrap: wrap;
    }

    .profile_box {
        width: 18vw;
        height: 24vw;
        display: inline-block;
        text-align: left;
    }

        .profile_box img {
            height: 83%;
            width: 90%;
            cursor: pointer;
            transition: all 0.5s ease;
        }

            .profile_box img:hover {
                transform: scale(1.1);
            }

        .profile_box .artist_name {
            width: 17vw;
            text-align: center;
            color: black;
            margin-bottom: 3px;
            display: block;
            font-size: 1.2vw;
        }

        .profile_box .state {
            border: solid;
            color: white;
            display: block;
        }
}

@media screen and (max-width: 584px) {

    .enter_below_the_email_address {
        color: white;
        text-align: left;
        margin-bottom: 5vw;
        margin-top: 4vw;
        font-size: 3vw;
    }
}

@media screen and (min-width: 585px) {

    .enter_below_the_email_address {
        color: white;
        text-align: left;
        margin-bottom: 1vw;
        font-size: 1.5vw;
    }
}


@media screen and (max-width: 584px) {

    .align_icons1 {
        margin-top: -1vw;
        margin-bottom: 6vw;
    }
}

@media screen and (min-width: 585px) {

    .align_icons1 {
        margin-top: -1.4vw;
        margin-bottom: 2.7vw;
    }
}