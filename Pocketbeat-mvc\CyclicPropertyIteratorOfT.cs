﻿namespace Pocketbeat_mvc
{
    public class CyclicPropertyIterator<T>
    {
        private List<T> items;
        private int currentIndex;

        public CyclicPropertyIterator(IEnumerable<T> inputItems)
        {
            items = new List<T>(inputItems);
            currentIndex = 0;
        }

        public T GetCurrent()
        {
            if (items.Count == 0)
                return default;

            T currentItem = items[currentIndex];
            currentIndex = (currentIndex + 1) % items.Count;
            return currentItem;
        }
    }
}
