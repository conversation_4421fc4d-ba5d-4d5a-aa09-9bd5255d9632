﻿


.container1 {
}

.main {
    text-align: center;
    width: 100%;
    margin: auto;
    position: relative;
}

    .main .back_button {
        width: 20px;
        height: 20px;
        position: absolute;
        top: 0;
        left: 0;
    }

        .main .back_button:hover {
            background-color: lightgray;
        }


.related_artists1 {
    height: 32vw;
    text-align: left;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
}

    .related_artists1 .artist_image {
        display: inline;
        height: 100%;
    }

@media screen and (min-width: 585px) {
    .section {
        margin-bottom: 6vw
    }
}

@media screen and (max-width: 584px) {
    .section {
        margin-bottom: 10vw
    }
}





@media screen and (min-width: 585px) {

    .track_title_and_artist {
        display: block;
        text-align: left;
        font-weight: 600;
        /*margin-left: 2.5vw;*/
        margin-bottom: 1vw;
        font-size: 1.2vw;
    }

        .track_title_and_artist > a {
            color: black;
            text-decoration: none;
        }
}

@media screen and (max-width: 584px) {

    .track_title_and_artist {
        display: block;
        text-align: left;
        font-weight: 600;
        /*margin-left: 2.5vw;*/
        margin-bottom: 1vw;
        font-size: 3vw;
    }

        .track_title_and_artist > a {
            color: black;
            text-decoration: none;
        }
}



.streaming_services {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 4vw;
    gap: 4vw;
    justify-content: flex-start;
}

    .streaming_services .straming_service_icon {
        width: 35px;
        height: 35px;
    }



@media screen and (max-width: 584px) {

    .main .track_name {
        display: inline-block;
        min-width: 100px;
        color: black;
        margin: 0;
        font-size: 4vw;
    }
}

@media screen and (min-width: 585px) {

    .main .track_name {
        display: inline-block;
        min-width: 100px;
        color: black;
        margin: 0;
        font-size: 2vw;
    }
}




.description {
    margin-top: 2vw;
    margin-bottom: 2vw;
    text-align: left;
    width: 100%;
}

.DescriptionTruncated {
    width: 100%;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    color: black;
    font-size: 16px;
    margin: auto;
    margin-top: 1vw;
    margin-bottom: 1vw;
    text-align: left;
}

.DescriptionExtended {
    width: 100%;
    display: none;
    font-size: 16px;
    margin: auto;
    margin-top: 1vw;
    margin-bottom: 1vw;
    text-align: left;
}


.button1 {
    width: 100%;
    display: block;
    margin: auto;
    background-color: white;
    text-size-adjust: auto;
}

    .button1:hover {
        background-color: lightgray;
    }






.video-container {
    position: relative;
    padding-bottom: 56.25%;
    /* 16:9 */
    height: 0;
    margin-bottom: 1vw;
}

    .video-container iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }

.sc_embedment_container {
    width: 100%;
}

@media screen and (max-width: 400px) {

    .desktopImage {
        display: none;
    }


    .mobileImage {
        display: block;
    }
}


@media screen and (min-width: 401px) {

    .desktopImage {
        display: block;
    }


    .mobileImage {
        display: none;
    }
}

@media screen and (max-width: 584px) {

    .section_title {
        text-align: left;
        margin-top: 2vw;
        margin-bottom: 2vw;
        font-weight: 900;
        font-size: 3vw;
    }
}

@media screen and (min-width: 585px) {

    .section_title {
        text-align: left;
        margin-top: 2vw;
        margin-bottom: 2vw;
        font-weight: 900;
        font-size: 1.2vw;
    }
}



.related_journals {
}

    .related_journals img {
        width: 100%;
    }



::-webkit-scrollbar {
    height: 4px;
    /* height of horizontal scrollbar ← You're missing this */
    width: 4px;
    /* width of vertical scrollbar */
    border: 1px solid #d5d5d5;
}




/*journal slide*/
.c {
    width: 100%;
    height: 500px;
    /* border-radius: 12px; */
    /* padding: 20px; */
    padding-bottom: 40px;
    /* box-shadow: 0 8px 48px 2px hsl(10deg 6% 15% / 40%); */
    display: flex;
    /* align-items: flex-end; */
    justify-content: center;
    position: relative;
    overflow: hidden;
    background: hsl(0 0% 90%);
    box-sizing: border-box;
}

.ci {
    position: absolute;
    top: 0;
    left: 0;
    width: inherit;
    height: inherit;
    transform-origin: left 50%;
    background: inherit;
    z-index: var(--z);
    transition: .3s ease-out;
}

    .ci img {
        -moz-user-select: none;
        user-select: none;
        height: 100%;
        width: 100%;
    }

.ch {
    position: absolute;
    top: 70%;
    left: 4%;
    transform: translateY(-50%);
    font-size: 2rem;
    color: hsla(var(--h) var(--s) var(--l) / .8);
    text-shadow: 0 2px 10px hsla(var(--h) var(--s) 10% / .3);
}


.slider_input {
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0;
    z-index: -10;
}

    .slider_input:last-child {
        margin-right: 0;
    }

    .slider_input:checked + label {
        /*background: linear-gradient(to right, hsla(var(--hue) 80% 70% / .7), hsla(calc(var(--hue) + 30) 80% 50% / .7));*/
        background-color: black;
    }

    .slider_input:not(:checked) + label + .ci {
        transform: translateX(-100%);
        opacity: 0;
    }

    .slider_input:checked + label + .ci ~ .ci {
        transform: translateX(100%);
    }

    .slider_input:not(:checked) + label + .ci {
        transition: 0;
    }

@media screen and (max-width: 584px) {

    .slider_label {
        width: 100%;
        height: 1.2vw;
        margin-right: 4px;
        /* border-radius: 20px; */
        background: hsla(0 0% 90% / .3);
        cursor: pointer;
        position: relative;
        z-index: 10;
    }
}

@media screen and (min-width: 585px) {

    .slider_label {
        width: 100%;
        height: 0.6vw;
        margin-right: 4px;
        /* border-radius: 20px; */
        background: hsla(0 0% 90% / .3);
        cursor: pointer;
        position: relative;
        z-index: 10;
    }
}



/*margin-top: -1vw;
margin-bottom: 2vw;*/

@media screen and (max-width: 584px) {

    .align_icons {
        margin-bottom: 8vw;
        margin-top: 4vw;
    }
}

@media screen and (min-width: 585px) {


    .align_icons {
        margin-top: 1vw;
        margin-bottom: 4vw;
    }
}
