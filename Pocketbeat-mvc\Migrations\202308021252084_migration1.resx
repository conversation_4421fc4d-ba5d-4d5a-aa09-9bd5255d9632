﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Target" xml:space="preserve">
    <value>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</value>
  </data>
  <data name="DefaultSchema" xml:space="preserve">
    <value>dbo</value>
  </data>
</root>