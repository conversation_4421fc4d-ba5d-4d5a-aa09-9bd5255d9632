﻿
var result = null;
var tempvar = null;

var promptOverlay = null;
var prompt = null;
var promptClose = null;

var allowResize = true;

var previousWidth = 0;



function IsInSameRange(previous, current) {

    if ((previous > 0 && previous <= 480) && (current > 0 && current <= 480)) {

        return true;
    }
    else if ((previous > 480 && previous <= 699) && (current > 480 && current <= 699)) {
        return true;
    }
    else if ((previous > 699 && previous <= 840) && (current > 699 && current <= 840)) {
        return true;
    }
    else if ((previous > 840) && (current > 840)) {
        return true;
    }
    else
        return false;

}

function EmailPrompt() {

    promptOverlay = document.getElementById('email-prompt-overlay');
    prompt = document.getElementById('email-prompt');
    promptClose = document.getElementById('cross_button');


    promptClose.addEventListener('click', () => {
        promptOverlay.style.display = 'none';
        prompt.style.display = 'none';
        document.body.style.overflow = 'auto';
    });

    promptOverlay.addEventListener('click', () => {
        promptOverlay.style.display = 'none';
        prompt.style.display = 'none';
        document.body.style.overflow = 'auto';
    });

    window.addEventListener('keydown', (event) => {
        if (event.key === 'Escape') {
            promptOverlay.style.display = 'none';
            prompt.style.display = 'none';
            document.body.style.overflow = 'auto';
        }
    });

}
function ClickEvntHndlForCovers() {
    var elements = document.querySelectorAll('.cover');

    // Add a click event handler to each element
    elements.forEach(function (element) {
        element.addEventListener('click', function () {

            var ytlink = element.getAttribute('href');
            var embedlink = ytlink.replace('watch?v=', 'embed/');
            //set the #youtube-video iframe's src
            document.getElementById("youtube-video").setAttribute('src', embedlink);
            document.getElementById("youtube-overlay").style.display = "block";

        });
    });
}
function YoutubeOverlayCloseEventAttach() {
    document.getElementById("close-youtube-overlay").addEventListener('click', function () {

        document.getElementById("youtube-overlay").style.display = "none";
        document.getElementById("youtube-video").setAttribute('src', '');

    });
}
function debounce(func, delay) {
    let timeoutId;
    return function () {
        const context = this;
        const args = arguments;
        clearTimeout(timeoutId);
        timeoutId = setTimeout(function () {
            func.apply(context, args);
        }, delay);
    };
}



function initialLoad() {



    var desiredSize = window.outerWidth;
    previousWidth = desiredSize;
    
    var qry = "";

    $('#loading-overlay').show();

    // Using jQuery AJAX
    $.ajax({
        url: '/Home/GetDynamicHtml',
        type: 'GET',
        data: { size: desiredSize }, // Pass the desired size here
        success: function (response) {
            $('#content').html(response);

            EmailPrompt();
            ClickEvntHndlForCovers();
            YoutubeOverlayCloseEventAttach();

        },
        error: function (response) {
            alert(response.responseText);
        },
        complete: function () {
            // Hide loading animation after AJAX call is complete (success or error)
            $('#loading-overlay').hide();
            
        }
    });
}


function resized() {

    
   
    var qry = "";

    $('#loading-overlay').show();

    // Using jQuery AJAX
    $.ajax({
        url: '/Home/GetDynamicHtml',
        type: 'GET',
        data: { size: previousWidth }, // Pass the desired size here
        success: function (response) {
            $('#content').html(response);

            EmailPrompt();
            ClickEvntHndlForCovers();
            YoutubeOverlayCloseEventAttach();

        },
        error: function (response) {
            alert(response.responseText);
        },
        complete: function () {
            // Hide loading animation after AJAX call is complete (success or error)
            $('#loading-overlay').hide();
            //alert(window.outerWidth);
        }
    });
}


function submitButton2Clicked() {

    var emailToSave = "";
    var countryToSave = "";

    emailToSave = document.getElementById("txt2").value;


    var selectedIndex = document.getElementById("country").selectedIndex;

    if (selectedIndex == 0) {

        countryToSave = "NA";

    }
    else {

        countryToSave = document.getElementById("country").options[selectedIndex].text;
    }


    SaveData(emailToSave, countryToSave);

    promptOverlay.style.display = 'none';
    prompt.style.display = 'none';
    document.body.style.overflow = 'auto';

    showPopup();
}
function EmailAlreadyExist(emailString) {

    result = null;

    $.ajax({
        type: "POST",
        url: "/Home/EmailExists",
        data: { param: emailString },
        async: false,
        success: function (response) {

            tempvar = response;


            if (response == true) {

                result = true;
            }
            else
                result = false;

        }
    }).fail(function (error) {

        result = error;

    });


}
function SaveData(email, country) {

    result = null;

    var data = {
        email: email,
        country: country
    };


    $.ajax({
        type: "POST",
        url: "/Home/SaveData",
        data: JSON.stringify(data),
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        success: function (response) {
            result = response

        }
    }).fail(function (error) {

        result = error;
    });
}
function displayPrompt() {
    promptOverlay.style.display = 'block';
    prompt.style.display = 'block';
    document.body.style.overflow = 'hidden';

}
function showPopup() {
    // Create the popup element
    var popup = document.createElement("div");
    popup.innerHTML = "Saved..";
    popup.style.position = "fixed";
    popup.style.left = "50%";
    popup.style.top = "50%";
    popup.style.transform = "translate(-50%, -50%)";
    popup.style.backgroundColor = "white";
    popup.style.border = "1px solid black";
    popup.style.padding = "10px";
    popup.style.color = "blue";
    popup.style.fontWeight = "900";

    // Add the popup to the page
    document.body.appendChild(popup);

    // Hide the popup after 1 second
    setTimeout(function () {
        popup.style.display = "none";
    }, 1000);
}
function myFunction() {
    var x = document.getElementById("myLinks");
    var hm_icon = document.getElementById("hamberger_icon");
    var close_icon = document.getElementById("close_icon");
    if (x.style.display === "block") {
        x.style.display = "none";
        hm_icon.style.display = "block";
        close_icon.style.display = "none";
    } else {
        x.style.display = "block";
        hm_icon.style.display = "none";
        close_icon.style.display = "block";
    }
}
function submitButton1Clicked(intput_elem_name) {

    var input = document.getElementById(intput_elem_name);

    if (input.checkValidity()) {
        // Get the value of the input field
        var value = input.value;



        EmailAlreadyExist(value)

        

        if (result == true) {

            //here we know that the email already exist.
            //so no action is taken. clear the txt element and return

            input.value = "";

            return;
        }



        //when we come to this stage we know that intput email
        //is valid and also it does not already exist.
        //so we should copy the txt to txt2,
        //hide #input_div and






        //set the value of txt to txt2
        document.getElementById("txt2").value = value;

        try {

            displayPrompt();

        }
        catch (Er) {
            alert(Er);
        }

        SaveData(input.value);


        input.value = "";

    }
    else { // when we come to here we know the input is not valid in #txt

        input.value = "";

        return;
    }
}





const inputElement = document.getElementById('txt');

inputElement.addEventListener('keyup', function (event) {
    allowResize = false;

    if (event.key === 'Enter') {
        event.preventDefault();



        var desiredSize = window.outerWidth;
        var searchQry = inputElement.value;



        //make an ajax request for html by passing screen width and search qury


        $.ajax({
            url: '/Home/GetDynamicHtml2',
            type: 'GET',
            data: { size: desiredSize, query: searchQry }, // Pass the desired size here
            success: function (response) {
                temp = response;
                $('#content').html(response);
                ClickEvntHndlForCovers();
                YoutubeOverlayCloseEventAttach();
                allowResize = true;

            },
            error: function (response) {
                alert(response.responseText);
            }
        });


        //make the topnav background color dark  and static
        var navbar = document.getElementById("topnav");
        navbar.style.position = "static";
        navbar.style.backgroundColor = "black";


        //hide menu
        var x = document.getElementById("myLinks");
        var hm_icon = document.getElementById("hamberger_icon");
        var close_icon = document.getElementById("close_icon");
        x.style.display = "none";
        hm_icon.style.display = "block";
        close_icon.style.display = "none";

    }
});

window.addEventListener("resize", function () {

    var res = IsInSameRange(previousWidth, window.outerWidth);

    if (res === false) {
        previousWidth = window.outerWidth;
        resized();
    }

});




initialLoad();




