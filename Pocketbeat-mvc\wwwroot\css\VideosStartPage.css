﻿.youtube-container {
    display: block;
    width: 100%;
    max-width: 600px;
    margin: 30px auto;
}



.youtube-player {
    display: block;
    margin: 20px auto;
    padding-bottom: 56.25%;
    overflow: hidden;
    position: relative;
    width: 100%;
    height: 100%;
    cursor: hand;
    cursor: pointer;
    display: block;
}

img.youtube-thumbnail {
    bottom: 0;
    display: block;
    left: 0;
    margin: auto;
    max-width: 100%;
    width: 100%;
    position: absolute;
    right: 0;
    top: 0;
    height: auto;
}



.youtube-iframe {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}


/* ------------------------------------------------------------------------------------ */

@media screen and (max-width: 584px) {
    .promo-area {
        margin-bottom: 3vw;
    }

        .promo-area .big-promo {
            width: 100%;
            position: relative;
        }

            .promo-area .big-promo img {
                width: 100%;
            }

        .promo-area .small-promo-area {
            height: 50vw;
            text-align: left;
            overflow-x: auto;
            overflow-y: hidden;
            white-space: nowrap;
            font-size:0;
        }

            .promo-area .small-promo-area .small-promo {
                display: inline-block;
                height: 100%;
                position: relative;
            }


                .promo-area .small-promo-area .small-promo img {
                    height: 100%;
                }

}

@media screen and (min-width: 585px) {
    .promo-area {
        margin-bottom: 3vw;
    }

        .promo-area .big-promo {
            width: 100%;
            position: relative;
        }

            .promo-area .big-promo img {
                width: 100%;
            }

        .promo-area .small-promo-area {
            height: 22vw;
            text-align: left;
            overflow-x: auto;
            overflow-y: hidden;
            white-space: nowrap;
            font-size: 0;
        }

            .promo-area .small-promo-area .small-promo {
                display: inline-block;
                height: 100%;
                position: relative;
            }


                .promo-area .small-promo-area .small-promo img {
                    height: 100%;
                }
}


div.youtube-play-btn {
    height: 6vw;
    width: 6vw;
    left: 50%;
    top: 50%;
    position: absolute;
    background: url('/icons/Play button.png') no-repeat center center;
    background-size: 6vw 6vw;
    transform: translate(-50%, -77%);
}



@media screen and (max-width: 584px) {

    .desktopImage {
        display: none;
    }


    .mobileImage {
        display: block;
    }
}


@media screen and (min-width: 585px) {

    .desktopImage {
        display: block;
    }


    .mobileImage {
        display: none;
    }
}

@media screen and (max-width: 584px) {

    .album-videos {
        height: 50vw;
        text-align: left;
        overflow-x: auto;
        overflow-y: hidden;
        white-space: nowrap;
        margin-left: 2vw;
        margin-bottom: 4vw;
    }

        .album-videos .poster {
            display: inline-block;
            height: 100%;
            position: relative;
        }

            .album-videos .poster img {
                height: 100%;
            }
}

@media screen and (min-width: 585px) {

    .album-videos {
        height: 22vw;
        text-align: left;
        overflow-x: auto;
        overflow-y: hidden;
        white-space: nowrap;
        margin-left: 2vw;
        margin-bottom: 4vw;
    }

        .album-videos .poster {
            display: inline-block;
            height: 100%;
            position: relative;
        }

            .album-videos .poster img {
                height: 100%;
            }
}


@media screen and (max-width: 584px) {


    .music_collection_name {
        text-align: left;
        font-weight: 600;
        margin-left: 2vw;
        margin-bottom: 1vw;
        font-size: 3vw;
    }
}


@media screen and (min-width: 585px) {


    .music_collection_name {
        text-align: left;
        font-weight: 600;
        margin-left: 2vw;
        margin-bottom: 1vw;
        font-size: 1.5vw;
    }
}


@media screen and (max-width: 584px) {

    .big-promo-button {
        position: absolute;
        right: 50%;
        top: 84%;
        transform: translate(50%,0);
        width: 24vw;
        height: 9.5vw;
        background-color: gray;
        opacity: 0.8;
        color: white;
        font-size: 3vw;
        border: white solid 0.5px;
    }
        .big-promo-button:hover {
            border: solid 0.5px;
            border-color: black;
        }
    
}


@media screen and (min-width: 585px) {

    .big-promo-button {
        position: absolute;
        right: 50%;
        top: 84%;
        transform: translate(50%,0);
        width: 11vw;
        height: 3.5vw;
        background-color: gray;
        opacity: 0.8;
        color: white;
        font-size: 1.5vw;
        border: white solid 0.5px;
    }


        .big-promo-button:hover {
            border: solid 0.5px;
            border-color: black;
        }
   
}





