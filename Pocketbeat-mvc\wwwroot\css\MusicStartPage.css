﻿#p_introduction {
    /* height: 90px; */
    font-size: 16px;
    /* min-width: 100%; */
    margin: 44px;
}

.profile_box_container {
    width: 100%;
    display: flex;
    justify-content: space-evenly;
    flex-wrap: wrap;
}

.profile_box {
    width: 150px;
    height: 280px;
    display: inline-block;
    text-align: center;
    margin: 14px;
}

    .profile_box img {
        height: 200px;
        width: 150px;
        cursor: pointer;
        transition: all 0.5s ease;
    }

        .profile_box img:hover {
            transform: scale(1.1);
        }

    .profile_box .artist_name {
        width: 150px;
        text-align: center;
        color: black;
        margin-bottom: 3px;
        display: block;
    }

    .profile_box .state {
        border: solid;
        color: white;
        display: block;
    }


    .profile_box .entity_name {
        width: 150px;
        text-align: center;
        color: white;
        margin-bottom: 6px;
        margin-top: 3px;
        display: block;
        font-size: small;
    }

    .profile_box .related_entity {
        width: 150px;
        text-align: center;
        color: white;
        margin-bottom: 3px;
        display: block;
        font-size: x-small;
    }

    .profile_box .cover_art_with_vinyls {
        cursor: pointer;
        transition: all 0.5s ease;
    }

        .profile_box .cover_art_with_vinyls:hover {
            transform: scale(1.1);
        }

        .profile_box .cover_art_with_vinyls img {
            height: 200px;
        }
