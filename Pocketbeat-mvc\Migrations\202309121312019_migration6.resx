﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Target" xml:space="preserve">
    <value>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</value>
  </data>
  <data name="DefaultSchema" xml:space="preserve">
    <value>dbo</value>
  </data>
</root>