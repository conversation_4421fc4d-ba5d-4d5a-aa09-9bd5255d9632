﻿using System.Data.Entity;

namespace Pocketbeat_mvc.Models
{
    public class Data :DbContext
    {
        public DbSet<Video> Videos { get; set; }

        public DbSet<Artist> Artists { get; set; }

        public DbSet<Admin> Admins { get; set; }


        public DbSet<Email> Emails { get; set; }

        public Data() : base(@"Data Source=(localdb)\mssqllocaldb;Initial Catalog=pocketbeat;Integrated Security=True")
        {

        }
    }
}
