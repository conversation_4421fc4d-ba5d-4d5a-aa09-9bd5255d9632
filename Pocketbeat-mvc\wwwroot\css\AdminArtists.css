﻿body {
    background-color: black;
}

form {
    position: relative;
}

#add_entity {
    position: relative;
    padding: 50px;
}


    #add_entity .close-button {
        position: absolute;
        top: 0;
        right: 50px;
        cursor: pointer;
        background-color: black;
        border: solid;
        color: white;
    }

#lbl_entity {
    color: white;
}


.btn1 {
    color: white;
    background-color: black;
    border-radius: 3px;
    border-color: white;
    padding-top: 11px;
    padding-bottom: 10px;
    padding-left: 20px;
    padding-right: 20px;
}



.profile_container {
    width: 800px;
}

.profile_box {
    width: 150px;
    height: 280px;
    display: inline-block;
    text-align: center;
    margin: 14px;
}

    .profile_box img {
        height: 200px;
        width: 150px;
        cursor: pointer;
        transition: all 0.5s ease;
    }

        .profile_box img:hover {
            transform: scale(1.1);
        }

    .profile_box .entity_name {
        width: 150px;
        text-align: center;
        color: white;
        margin-bottom: 3px;
        display: block;
    }

    .profile_box .state {
        border: solid;
        color: white;
        display: block;
    }

.infobox {
    margin-top: 15px;
    /*margin-left: 50px;*/
    /* height: 60px;*/
    min-height: 60px;
    background-color: black;
    color: white;
    border: solid white;
    border-width: 1px;
}

    .infobox label {
        width: 100%;
        height: 10px;
        font-size: x-small;
        font-size: x-small;
        display: block;
    }

    .infobox input {
        width: 90%;
        font-size: large;
        background-color: black;
        color: white;
        border: none;
    }

    .infobox .input_list {
        border: solid;
        border-color: #423a3a;
        margin-bottom: 2px;
    }

    .infobox textarea {
        font-size: large;
        background-color: black;
        color: white;
        border: none;
        width: 700px;
        height: 140px;
    }


.button_container {
    /*margin-left: 50px;*/
    margin-top: 20px;
}

#pic_vertical {
    width: 130px;
    height: 200px;
    position: relative;
    border: solid;
    border-color: white;
    color: white;
    /*margin-left: 50px;*/
    vertical-align: central;
    display: inline-block;
    background-size: 100% 100%;
}

    #pic_vertical:hover {
        background-color: #5d6952;
    }

#pic_horizontal {
    width: 200px;
    height: 130px;
    position: relative;
    border: solid;
    border-color: white;
    color: white;
    display: inline-block;
    background-size: 100% 100%;
}

    #pic_horizontal:hover {
        background-color: #5d6952;
    }




.plus {
    font-size: 73px;
    text-align: center;
    margin: auto;
    color: white;
    font-weight: 900;
}


.add_another {
    display: inline;
    text-align: right;
    color: white;
    position: absolute;
    right: 0px;
    font-size: x-small;
}

    .add_another:hover {
        color: blue;
    }

.select_related {
    background-color: black;
    color: white;
    margin: 3px;
    position: relative;
}

/*.select_related .close-button {
        position: absolute;
        top: 0;
        right: 0;
        display: none;
    }

    .select_related:hover .close-button {
        display: block;
    }

.close-button {*/
/* style the close button, such as color, size, etc. */
/*color:red;
}*/


.parent {
    z-index: 1;
    background-image: url('http://img.freepik.com/free-vector/white-canvas-background_1053-239.jpg?size=338&ext=jpg');
}

.child {
    position: absolute;
    z-index: 1;
}



.remove {
    position: absolute;
    top: 0px;
    right: 0px;
    display: block;
    box-sizing: border-box;
    width: 20px;
    height: 20px;
    border-width: 3px;
    border-style: solid;
    border-color: red;
    border-radius: 100%;
    background: -webkit-linear-gradient(-45deg, transparent 0%, transparent 46%, white 46%, white 56%,transparent 56%, transparent 100%), -webkit-linear-gradient(45deg, transparent 0%, transparent 46%, white 46%, white 56%,transparent 56%, transparent 100%);
    background-color: red;
    box-shadow: 0px 0px 5px 2px rgba(0,0,0,0.5);
    transition: all 0.3s ease;
}

