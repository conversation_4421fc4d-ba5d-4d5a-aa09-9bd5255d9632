﻿function ReadMoreButtonClicked(clicked_id) {

    var journal_id = clicked_id.split('_')[1];
    var DescriptionTruncated_id = "DescriptionTruncated_" + journal_id;
    var DescriptionExtended_id = "DescriptionExtended_" + journal_id;
    var DescriptionTruncated = document.getElementById(DescriptionTruncated_id);
    var DescriptionExtended = document.getElementById(DescriptionExtended_id);
    var ReadMoreButton = document.getElementById(clicked_id);


     if (ReadMoreButton.textContent == 'READ MORE') {
        DescriptionTruncated.style.display = 'none';
        DescriptionExtended.style.display = 'block';
        ReadMoreButton.textContent = 'READ LESS';
    } else {
        DescriptionTruncated.style.display = '-webkit-box';
        DescriptionExtended.style.display = 'none';
        ReadMoreButton.textContent = 'READ MORE';
    }
}