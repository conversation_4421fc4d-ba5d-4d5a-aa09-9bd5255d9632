﻿@model Pocketbeat_mvc.ViewModels.PartialViewModel

@{

    List<Video> videos = null;
    List<Video> selectedVideos = new List<Video>();
    using (Data data = new Data())
    {
        videos = data.Videos.Include("RelatedArtists").ToList();

        if (Model.SearchQuery != "")
        {
            foreach (var video in videos)
            {
                if (video.VideoTitle.Contains(Model.SearchQuery, StringComparison.InvariantCultureIgnoreCase) || video.Genres.Contains(Model.SearchQuery, StringComparison.InvariantCultureIgnoreCase) || video.RelatedArtists.Any(a => a.ArtistName.Contains(Model.SearchQuery, StringComparison.InvariantCultureIgnoreCase)))
                {
                    selectedVideos.Add(video);
                }
            }
            selectedVideos = selectedVideos.OrderByDescending(v => v.CreatedDate).ToList();

        }
        else // Model.SearchQuery == ""
        {
            selectedVideos = videos.OrderByDescending(v => v.CreatedDate).ToList();
        }

    }
}

@if (Model.SearchQuery == "")
{
    <div class="topmost">
        <img class="cover" href="@selectedVideos[0].YoutubeLink" src="Uploads\\@selectedVideos[0].VerticalImage" />
        <div class="genres">
            @selectedVideos[0].Genres
        </div>
    </div>

    <div style="display:grid; grid-template-columns: 1fr 1fr; gap:1vw">

        <div id="left">
            <div class="grid-2-cols" style="margin-right: 0vw;">
                <div class="col">
                    <div>
                        <img class="cover" href="@selectedVideos[1].YoutubeLink" src="Uploads\\@selectedVideos[1].HorizontalImageSmall" />
                    </div>
                    <div class="genres">
                        @selectedVideos[1].Genres
                    </div>
                    <div>
                        <img class="cover" href="@selectedVideos[5].YoutubeLink" src="Uploads\\@selectedVideos[5].VerticalImageSmall" />
                    </div>
                    <div class="genres">@selectedVideos[5].Genres</div>
                    <div>
                        <img class="cover" href="@selectedVideos[9].YoutubeLink" src="Uploads\\@selectedVideos[9].SquareImageSmall" />
                    </div>
                    <div class="genres">@selectedVideos[9].Genres</div>
                </div>

                <div class="col">
                    <div>
                        <img class="cover" href="@selectedVideos[2].YoutubeLink" src="Uploads\\@selectedVideos[2].SquareImageSmall" />
                    </div>
                    <div class="genres">
                        @selectedVideos[2].Genres
                    </div>
                    <div>
                        <img class="cover" href="@selectedVideos[6].YoutubeLink" src="Uploads\\@selectedVideos[6].HorizontalImageSmall" />
                    </div>
                    <div class="genres">@selectedVideos[6].Genres</div>
                    <div>
                        <img class="cover" href="@selectedVideos[10].YoutubeLink" src="Uploads\\@selectedVideos[10].VerticalImageSmall" />
                    </div>
                    <div class="genres">@selectedVideos[10].Genres</div>
                </div>
            </div>

            @*//subscribe section*@
            <div class="sub-to-youtube" style="background-image:url('/Images/2colon1youtubeprompt.jpg');background-size: cover;">
                <div class="subscribe_desc">SUBSCRIBE TO OUR <br> YOUTUBE CHANNELS</div>

                <div class="scrollable-image-menu">
                    <!-- this div is the scrollable image menu -->
                    <a href="https://www.youtube.com/@@PocketbeatHard" target="_blank">
                        <div class="icon_and_name">
                            <div class="icon_container">
                                <img class="yt-icon"
                                     src="~/Images/YouTube profile picture Hard channel NEW.jpg" />
                            </div>
                            <div class="channel_name"> HARD</div>
                        </div>
                    </a>

                    <a href="https://www.youtube.com/@@PocketbeatHouse" target="_blank">
                        <div class="icon_and_name">
                            <div class="icon_container">
                                <img class="yt-icon" src="~/Images/YouTube profile picture House channel NEW.jpg" />
                            </div>
                            <div class="channel_name">HOUSE</div>
                        </div>
                    </a>

                    <a href="https://www.youtube.com/@@Pocketbeat" target="_blank">
                        <div class="icon_and_name">
                            <div class="icon_container">
                                <img class="yt-icon"
                                     src="~/Images/YouTube profile picture Original channel NEW.jpg" />
                            </div>
                            <div class="channel_name">ORIGINAL</div>
                        </div>
                    </a>

                    <a href="https://www.youtube.com/@@PocketbeatPsy" target="_blank">
                        <div class="icon_and_name">
                            <div class="icon_container">
                                <img class="yt-icon" src="~/Images/YouTube profile picture Psy channel NEW.jpg" />
                            </div>
                            <div class="channel_name">PSY</div>
                        </div>
                    </a>

                    <a href="https://www.youtube.com/@@PocketbeatTechno" target="_blank">
                        <div class="icon_and_name">
                            <div class="icon_container">
                                <img class="yt-icon"
                                     src="~/Images/YouTube profile picture Techno channel NEW.jpg" />
                            </div>
                            <div class="channel_name">TECHNO</div>
                        </div>
                    </a>

                    <a href="https://www.youtube.com/@@PocketbeatTrance" target="_blank">
                        <div class="icon_and_name">
                            <div class="icon_container">
                                <img class="yt-icon" src="~/Images/YouTube profile picture Trance channel NEW.jpg" />
                            </div>
                            <div class="channel_name">TRANCE</div>
                        </div>
                    </a>
                </div>
            </div>


            <div class="grid-2-cols" style="margin-right: 0vw;margin-top: -4vw;">
                <div class="col">
                    @{
                        var cyclicIterator = new CyclicStringIterator(new List<string> { "hor", "ver", "sqr" });

                        int index = 13;

                        for (index = 13; index < selectedVideos.Count; index += 4)
                        {

                            var current = cyclicIterator.GetCurrent();
                            string imagePath = "";

                            if (current == "hor")
                            {
                                imagePath = $"Uploads\\{selectedVideos[index].HorizontalImageSmall}";
                            }
                            else if (current == "ver")
                            {
                                imagePath = $"Uploads\\{selectedVideos[index].VerticalImageSmall}";
                            }
                            else
                            {
                                imagePath = $"Uploads\\{selectedVideos[index].SquareImageSmall}";
                            }


                            <div>
                                <img class="cover" href="@selectedVideos[index].YoutubeLink" src="@imagePath" />
                            </div>
                            <div class="genres">
                                @selectedVideos[index].Genres
                            </div>





                        }

                    }
                </div>
                <div class="col">
                    @{
                        var cyclicIterator2 = new CyclicStringIterator(new List<string> { "sqr", "hor", "ver" });

                        index = 14;

                        for (index = 14; index < selectedVideos.Count; index += 4)
                        {

                            var current = cyclicIterator2.GetCurrent();
                            string imagePath = "";

                            if (current == "hor")
                            {
                                imagePath = $"Uploads\\{selectedVideos[index].HorizontalImageSmall}";
                            }
                            else if (current == "ver")
                            {
                                imagePath = $"Uploads\\{selectedVideos[index].VerticalImageSmall}";
                            }
                            else
                            {
                                imagePath = $"Uploads\\{selectedVideos[index].SquareImageSmall}";
                            }


                            <div>
                                <img class="cover" href="@selectedVideos[index].YoutubeLink" src="@imagePath" />
                            </div>
                            <div class="genres">
                                @selectedVideos[index].Genres
                            </div>





                        }

                    }
                </div>
            </div>
        </div>

        <div id="right">
            <div class="grid-2-cols" style="margin-left: 0;">
                <div class="col">
                    @*col3*@
                    <div>
                        <img class="cover" href="@selectedVideos[3].YoutubeLink" src="Uploads\\@selectedVideos[3].VerticalImageSmall" />
                    </div>
                    <div class="genres">
                        @selectedVideos[3].Genres
                    </div>
                    <div>
                        <img class="cover" href="@selectedVideos[7].YoutubeLink" src="Uploads\\@selectedVideos[7].SquareImageSmall" />
                    </div>
                    <div class="genres">@selectedVideos[7].Genres</div>

                </div>

                <div class="col">
                    @*col4*@
                    <div>
                        <img class="cover" href="@selectedVideos[4].YoutubeLink" src="Uploads\\@selectedVideos[4].HorizontalImageSmall" />
                    </div>
                    <div class="genres">
                        @selectedVideos[4].Genres
                    </div>

                </div>
            </div>

            @* email box*@
            <div class="email-box" style="background-image:url('/Images/1point85colon1.jpg');background-size: cover;">
                <div class="email-box-desc">GET THE LATEST DROPS FIRST. <br> JOIN OUR NEWLETTER.</div>
                <input class="email_txt" id="email_input2" type="email" placeholder="Enter your email address" required>
                <input type="button" onclick="submitButton1Clicked('email_input2');" class="email_button" value="JOIN">
            </div>

            <div class="grid-2-cols" style="margin-left: 0;margin-top: -5vw;">
                <div class="col3">
                    @{
                        var cyclicIterator3 = new CyclicStringIterator(new List<string> { "ver", "sqr", "hor" });

                        index = 11;

                        for (index = 11; index < selectedVideos.Count; index += 4)
                        {

                            var current = cyclicIterator3.GetCurrent();
                            string imagePath = "";

                            if (current == "hor")
                            {
                                imagePath = $"Uploads\\{selectedVideos[index].HorizontalImageSmall}";
                            }
                            else if (current == "ver")
                            {
                                imagePath = $"Uploads\\{selectedVideos[index].VerticalImageSmall}";
                            }
                            else
                            {
                                imagePath = $"Uploads\\{selectedVideos[index].SquareImageSmall}";
                            }


                            <div>
                                <img class="cover" href="@selectedVideos[index].YoutubeLink" src="@imagePath" />
                            </div>
                            <div class="genres">
                                @selectedVideos[index].Genres
                            </div>

                        }

                    }
                </div>
                <div class="col4">
                    @{
                        var cyclicIterator4 = new CyclicStringIterator(new List<string> { "hor", "sqr", "ver" });

                        index = 12;

                        for (index = 12; index < selectedVideos.Count; index += 4)
                        {

                            var current = cyclicIterator4.GetCurrent();
                            string imagePath = "";

                            if (current == "hor")
                            {
                                imagePath = $"Uploads\\{selectedVideos[index].HorizontalImageSmall}";
                            }
                            else if (current == "ver")
                            {
                                imagePath = $"Uploads\\{selectedVideos[index].VerticalImageSmall}";
                            }
                            else
                            {
                                imagePath = $"Uploads\\{selectedVideos[index].SquareImageSmall}";
                            }


                            <div>
                                <img class="cover" href="@selectedVideos[index].YoutubeLink" src="@imagePath" />
                            </div>
                            <div class="genres">
                                @selectedVideos[index].Genres
                            </div>

                        }

                    }
                </div>

            </div>


        </div>


    </div>


}
else
{
    <div class="four-cols">
        <div class="col1">
            @{
                var cyclicIterator = new CyclicStringIterator(new List<string> { "hor", "ver", "sqr" });

                int index = 0;

                for (index = 0; index < selectedVideos.Count; index += 4)
                {

                    var current = cyclicIterator.GetCurrent();
                    string imagePath = "";

                    if (current == "hor")
                    {
                        imagePath = $"Uploads\\{selectedVideos[index].HorizontalImageSmall}";
                    }
                    else if (current == "ver")
                    {
                        imagePath = $"Uploads\\{selectedVideos[index].VerticalImageSmall}";
                    }
                    else
                    {
                        imagePath = $"Uploads\\{selectedVideos[index].SquareImageSmall}";
                    }


                    <div>
                        <img class="cover" href="@selectedVideos[index].YoutubeLink" src="@imagePath" />
                    </div>
                    <div class="genres">
                        @selectedVideos[index].Genres
                    </div>





                }

            }
        </div>
        <div class="col2">
            @{
                var cyclicIterator2 = new CyclicStringIterator(new List<string> { "sqr", "hor", "ver" });

                index = 1;

                for (index = 1; index < selectedVideos.Count; index += 4)
                {

                    var current = cyclicIterator2.GetCurrent();
                    string imagePath = "";

                    if (current == "hor")
                    {
                        imagePath = $"Uploads\\{selectedVideos[index].HorizontalImageSmall}";
                    }
                    else if (current == "ver")
                    {
                        imagePath = $"Uploads\\{selectedVideos[index].VerticalImageSmall}";
                    }
                    else
                    {
                        imagePath = $"Uploads\\{selectedVideos[index].SquareImageSmall}";
                    }


                    <div>
                        <img class="cover" href="@selectedVideos[index].YoutubeLink" src="@imagePath" />
                    </div>
                    <div class="genres">
                        @selectedVideos[index].Genres
                    </div>





                }

            }
        </div>
        <div class="col3">
            @{
                var cyclicIterator3 = new CyclicStringIterator(new List<string> { "ver", "sqr", "hor", });

                index = 2;

                for (index = 2; index < selectedVideos.Count; index += 4)
                {

                    var current = cyclicIterator3.GetCurrent();
                    string imagePath = "";

                    if (current == "hor")
                    {
                        imagePath = $"Uploads\\{selectedVideos[index].HorizontalImageSmall}";
                    }
                    else if (current == "ver")
                    {
                        imagePath = $"Uploads\\{selectedVideos[index].VerticalImageSmall}";
                    }
                    else
                    {
                        imagePath = $"Uploads\\{selectedVideos[index].SquareImageSmall}";
                    }


                    <div>
                        <img class="cover" href="@selectedVideos[index].YoutubeLink" src="@imagePath" />
                    </div>
                    <div class="genres">
                        @selectedVideos[index].Genres
                    </div>





                }

            }
        </div>
        <div class="col4">
            @{
                var cyclicIterator4 = new CyclicStringIterator(new List<string> { "hor", "sqr", "ver" });

                index = 3;

                for (index = 3; index < selectedVideos.Count; index += 4)
                {

                    var current = cyclicIterator4.GetCurrent();
                    string imagePath = "";

                    if (current == "hor")
                    {
                        imagePath = $"Uploads\\{selectedVideos[index].HorizontalImageSmall}";
                    }
                    else if (current == "ver")
                    {
                        imagePath = $"Uploads\\{selectedVideos[index].VerticalImageSmall}";
                    }
                    else
                    {
                        imagePath = $"Uploads\\{selectedVideos[index].SquareImageSmall}";
                    }


                    <div>
                        <img class="cover" href="@selectedVideos[index].YoutubeLink" src="@imagePath" />
                    </div>
                    <div class="genres">
                        @selectedVideos[index].Genres
                    </div>





                }

            }
        </div>
    </div>
}


@if (Model.SearchQuery == "")
{
    <div id="email-prompt-overlay"></div>
    <div id="email-prompt">
        <button id="cross_button">&#x2715;</button>

        <div id="div_new">
            <select id="country" class="country" name="country">
                <option>Select country (Optional)</option>
                <option value="AF">Afghanistan</option>
                <option value="AX">Aland Islands</option>
                <option value="AL">Albania</option>
                <option value="DZ">Algeria</option>
                <option value="AS">American Samoa</option>
                <option value="AD">Andorra</option>
                <option value="AO">Angola</option>
                <option value="AI">Anguilla</option>
                <option value="AQ">Antarctica</option>
                <option value="AG">Antigua and Barbuda</option>
                <option value="AR">Argentina</option>
                <option value="AM">Armenia</option>
                <option value="AW">Aruba</option>
                <option value="AU">Australia</option>
                <option value="AT">Austria</option>
                <option value="AZ">Azerbaijan</option>
                <option value="BS">Bahamas</option>
                <option value="BH">Bahrain</option>
                <option value="BD">Bangladesh</option>
                <option value="BB">Barbados</option>
                <option value="BY">Belarus</option>
                <option value="BE">Belgium</option>
                <option value="BZ">Belize</option>
                <option value="BJ">Benin</option>
                <option value="BM">Bermuda</option>
                <option value="BT">Bhutan</option>
                <option value="BO">Bolivia</option>
                <option value="BQ">Bonaire, Sint Eustatius and Saba</option>
                <option value="BA">Bosnia and Herzegovina</option>
                <option value="BW">Botswana</option>
                <option value="BV">Bouvet Island</option>
                <option value="BR">Brazil</option>
                <option value="IO">British Indian Ocean Territory</option>
                <option value="BN">Brunei Darussalam</option>
                <option value="BG">Bulgaria</option>
                <option value="BF">Burkina Faso</option>
                <option value="BI">Burundi</option>
                <option value="KH">Cambodia</option>
                <option value="CM">Cameroon</option>
                <option value="CA">Canada</option>
                <option value="CV">Cape Verde</option>
                <option value="KY">Cayman Islands</option>
                <option value="CF">Central African Republic</option>
                <option value="TD">Chad</option>
                <option value="CL">Chile</option>
                <option value="CN">China</option>
                <option value="CX">Christmas Island</option>
                <option value="CC">Cocos (Keeling) Islands</option>
                <option value="CO">Colombia</option>
                <option value="KM">Comoros</option>
                <option value="CG">Congo</option>
                <option value="CD">Congo, Democratic Republic of the Congo</option>
                <option value="CK">Cook Islands</option>
                <option value="CR">Costa Rica</option>
                <option value="CI">Cote D'Ivoire</option>
                <option value="HR">Croatia</option>
                <option value="CU">Cuba</option>
                <option value="CW">Curacao</option>
                <option value="CY">Cyprus</option>
                <option value="CZ">Czech Republic</option>
                <option value="DK">Denmark</option>
                <option value="DJ">Djibouti</option>
                <option value="DM">Dominica</option>
                <option value="DO">Dominican Republic</option>
                <option value="EC">Ecuador</option>
                <option value="EG">Egypt</option>
                <option value="SV">El Salvador</option>
                <option value="GQ">Equatorial Guinea</option>
                <option value="ER">Eritrea</option>
                <option value="EE">Estonia</option>
                <option value="ET">Ethiopia</option>
                <option value="FK">Falkland Islands (Malvinas)</option>
                <option value="FO">Faroe Islands</option>
                <option value="FJ">Fiji</option>
                <option value="FI">Finland</option>
                <option value="FR">France</option>
                <option value="GF">French Guiana</option>
                <option value="PF">French Polynesia</option>
                <option value="TF">French Southern Territories</option>
                <option value="GA">Gabon</option>
                <option value="GM">Gambia</option>
                <option value="GE">Georgia</option>
                <option value="DE">Germany</option>
                <option value="GH">Ghana</option>
                <option value="GI">Gibraltar</option>
                <option value="GR">Greece</option>
                <option value="GL">Greenland</option>
                <option value="GD">Grenada</option>
                <option value="GP">Guadeloupe</option>
                <option value="GU">Guam</option>
                <option value="GT">Guatemala</option>
                <option value="GG">Guernsey</option>
                <option value="GN">Guinea</option>
                <option value="GW">Guinea-Bissau</option>
                <option value="GY">Guyana</option>
                <option value="HT">Haiti</option>
                <option value="HM">Heard Island and Mcdonald Islands</option>
                <option value="VA">Holy See (Vatican City State)</option>
                <option value="HN">Honduras</option>
                <option value="HK">Hong Kong</option>
                <option value="HU">Hungary</option>
                <option value="IS">Iceland</option>
                <option value="IN">India</option>
                <option value="ID">Indonesia</option>
                <option value="IR">Iran, Islamic Republic of</option>
                <option value="IQ">Iraq</option>
                <option value="IE">Ireland</option>
                <option value="IM">Isle of Man</option>
                <option value="IL">Israel</option>
                <option value="IT">Italy</option>
                <option value="JM">Jamaica</option>
                <option value="JP">Japan</option>
                <option value="JE">Jersey</option>
                <option value="JO">Jordan</option>
                <option value="KZ">Kazakhstan</option>
                <option value="KE">Kenya</option>
                <option value="KI">Kiribati</option>
                <option value="KP">Korea, Democratic People's Republic of</option>
                <option value="KR">Korea, Republic of</option>
                <option value="XK">Kosovo</option>
                <option value="KW">Kuwait</option>
                <option value="KG">Kyrgyzstan</option>
                <option value="LA">Lao People's Democratic Republic</option>
                <option value="LV">Latvia</option>
                <option value="LB">Lebanon</option>
                <option value="LS">Lesotho</option>
                <option value="LR">Liberia</option>
                <option value="LY">Libyan Arab Jamahiriya</option>
                <option value="LI">Liechtenstein</option>
                <option value="LT">Lithuania</option>
                <option value="LU">Luxembourg</option>
                <option value="MO">Macao</option>
                <option value="MK">Macedonia, the Former Yugoslav Republic of</option>
                <option value="MG">Madagascar</option>
                <option value="MW">Malawi</option>
                <option value="MY">Malaysia</option>
                <option value="MV">Maldives</option>
                <option value="ML">Mali</option>
                <option value="MT">Malta</option>
                <option value="MH">Marshall Islands</option>
                <option value="MQ">Martinique</option>
                <option value="MR">Mauritania</option>
                <option value="MU">Mauritius</option>
                <option value="YT">Mayotte</option>
                <option value="MX">Mexico</option>
                <option value="FM">Micronesia, Federated States of</option>
                <option value="MD">Moldova, Republic of</option>
                <option value="MC">Monaco</option>
                <option value="MN">Mongolia</option>
                <option value="ME">Montenegro</option>
                <option value="MS">Montserrat</option>
                <option value="MA">Morocco</option>
                <option value="MZ">Mozambique</option>
                <option value="MM">Myanmar</option>
                <option value="NA">Namibia</option>
                <option value="NR">Nauru</option>
                <option value="NP">Nepal</option>
                <option value="NL">Netherlands</option>
                <option value="AN">Netherlands Antilles</option>
                <option value="NC">New Caledonia</option>
                <option value="NZ">New Zealand</option>
                <option value="NI">Nicaragua</option>
                <option value="NE">Niger</option>
                <option value="NG">Nigeria</option>
                <option value="NU">Niue</option>
                <option value="NF">Norfolk Island</option>
                <option value="MP">Northern Mariana Islands</option>
                <option value="NO">Norway</option>
                <option value="OM">Oman</option>
                <option value="PK">Pakistan</option>
                <option value="PW">Palau</option>
                <option value="PS">Palestinian Territory, Occupied</option>
                <option value="PA">Panama</option>
                <option value="PG">Papua New Guinea</option>
                <option value="PY">Paraguay</option>
                <option value="PE">Peru</option>
                <option value="PH">Philippines</option>
                <option value="PN">Pitcairn</option>
                <option value="PL">Poland</option>
                <option value="PT">Portugal</option>
                <option value="PR">Puerto Rico</option>
                <option value="QA">Qatar</option>
                <option value="RE">Reunion</option>
                <option value="RO">Romania</option>
                <option value="RU">Russian Federation</option>
                <option value="RW">Rwanda</option>
                <option value="BL">Saint Barthelemy</option>
                <option value="SH">Saint Helena</option>
                <option value="KN">Saint Kitts and Nevis</option>
                <option value="LC">Saint Lucia</option>
                <option value="MF">Saint Martin</option>
                <option value="PM">Saint Pierre and Miquelon</option>
                <option value="VC">Saint Vincent and the Grenadines</option>
                <option value="WS">Samoa</option>
                <option value="SM">San Marino</option>
                <option value="ST">Sao Tome and Principe</option>
                <option value="SA">Saudi Arabia</option>
                <option value="SN">Senegal</option>
                <option value="RS">Serbia</option>
                <option value="CS">Serbia and Montenegro</option>
                <option value="SC">Seychelles</option>
                <option value="SL">Sierra Leone</option>
                <option value="SG">Singapore</option>
                <option value="SX">Sint Maarten</option>
                <option value="SK">Slovakia</option>
                <option value="SI">Slovenia</option>
                <option value="SB">Solomon Islands</option>
                <option value="SO">Somalia</option>
                <option value="ZA">South Africa</option>
                <option value="GS">South Georgia and the South Sandwich Islands</option>
                <option value="SS">South Sudan</option>
                <option value="ES">Spain</option>
                <option value="LK">Sri Lanka</option>
                <option value="SD">Sudan</option>
                <option value="SR">Suriname</option>
                <option value="SJ">Svalbard and Jan Mayen</option>
                <option value="SZ">Swaziland</option>
                <option value="SE">Sweden</option>
                <option value="CH">Switzerland</option>
                <option value="SY">Syrian Arab Republic</option>
                <option value="TW">Taiwan, Province of China</option>
                <option value="TJ">Tajikistan</option>
                <option value="TZ">Tanzania, United Republic of</option>
                <option value="TH">Thailand</option>
                <option value="TL">Timor-Leste</option>
                <option value="TG">Togo</option>
                <option value="TK">Tokelau</option>
                <option value="TO">Tonga</option>
                <option value="TT">Trinidad and Tobago</option>
                <option value="TN">Tunisia</option>
                <option value="TR">Turkey</option>
                <option value="TM">Turkmenistan</option>
                <option value="TC">Turks and Caicos Islands</option>
                <option value="TV">Tuvalu</option>
                <option value="UG">Uganda</option>
                <option value="UA">Ukraine</option>
                <option value="AE">United Arab Emirates</option>
                <option value="GB">United Kingdom</option>
                <option value="US">United States</option>
                <option value="UM">United States Minor Outlying Islands</option>
                <option value="UY">Uruguay</option>
                <option value="UZ">Uzbekistan</option>
                <option value="VU">Vanuatu</option>
                <option value="VE">Venezuela</option>
                <option value="VN">Viet Nam</option>
                <option value="VG">Virgin Islands, British</option>
                <option value="VI">Virgin Islands, U.s.</option>
                <option value="WF">Wallis and Futuna</option>
                <option value="EH">Western Sahara</option>
                <option value="YE">Yemen</option>
                <option value="ZM">Zambia</option>
                <option value="ZW">Zimbabwe</option>
            </select>
            <div id="under_country">
                <b>Why do you want to know where I live?</b>
                <br>
                Because when we plan to throw our next bad ass party in your country you
                will be the first to know.
            </div>
            <div id="input_div2">
                <input id="txt2" class="prompt_email_txt" type="email" placeholder="Enter your email address" required>
                <input type="button" onclick="submitButton2Clicked();" class="prompt_email_button" id="btn2" value="SUBMIT">
            </div>
        </div>
    </div>
}
